AllCops:
  TargetRubyVersion: 3.4.4
  NewCops: enable
  SuggestExtensions: false
  Exclude:
    - 'vendor/**/*'
    - 'simulation/vendor/**/*'

# Prefer double-quoted strings
Style/StringLiterals:
  EnforcedStyle: double_quotes

# Allow string concatenation for colorized output
Style/StringConcatenation:
  Enabled: false

# Allow blocks with many lines
Metrics/BlockLength:
  Enabled: false

# Allow longer methods for plugin code
Metrics/MethodLength:
  Enabled: false

# Allow more complex methods for plugin logic
Metrics/CyclomaticComplexity:
  Enabled: false

# Allow more ABC complexity for plugin logic
Metrics/AbcSize:
  Enabled: false

# Allow classes with more lines
Metrics/ClassLength:
  Enabled: false

# Disable Documentation requirement
Style/Documentation:
  Enabled: false

# Allow empty methods
Style/EmptyMethod:
  Enabled: false

# Disable frozen string literal requirement
Style/FrozenStringLiteralComment:
  Enabled: false

# Allow trailing empty lines
Layout/TrailingEmptyLines:
  Enabled: false

# Allow multiple assignment
Style/ParallelAssignment:
  Enabled: false

Metrics/PerceivedComplexity:
  Enabled: false

Layout/LineLength:
  Enabled: false

Metrics/ModuleLength:
  Enabled: false
