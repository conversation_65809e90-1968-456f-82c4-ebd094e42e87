# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Quick Start
```bash
rake install              # Install Ruby dependencies
rake server               # Start development server at http://localhost:4567
```

### Available Rake Tasks
```bash
rake install              # Install Ruby dependencies
rake server               # Start development server
rake test                 # Run plugin setup test
rake rubocop              # Run style checks
rake rubocop_fix          # Auto-fix style issues
rake help                 # Show available tasks
```

### Manual Development
```bash
cd simulation
bundle install            # Install Ruby dependencies
ruby server.rb            # Start development server (port 4567)
```

## Project Architecture

This is a **Ruby-based TRMNL plugin** for displaying Notion content on e-ink displays. The project uses a custom simulation environment rather than traditional web frameworks.

### Core Structure
- **Language**: Ruby with ERB templates
- **Main Plugin**: `/lib/notion/notion.rb` - Main plugin class extending `Plugins::Base`
- **Data Provider**: `/lib/notion/data_provider.rb` - Notion API integration and mock data handling
- **Configuration**: `/lib/notion/form_fields.yaml` - 6 user settings for integration token, database/page IDs, display options
- **Templates**: `/lib/notion/views/` - 4 responsive layouts (full, half_vertical, half_horizontal, quadrant)
- **Mock System**: `/lib/notion/mock_data.rb` - Complete mock data for development

### Plugin Components
- **Dual Content Types**: Support for both Notion databases and pages
- **E-ink Optimized Layouts**: 4 responsive layouts for different display sizes
- **Rich Content Support**: Headers, lists, todos, code blocks, quotes with e-ink optimization

### Key Dependencies (Gemfile)
- `activesupport ~> 7.0` - Core utilities and extensions
- `httparty ~> 0.21` - HTTP client for Notion API calls
- `sinatra ~> 3.0` - Web server for simulation environment
- `erb ~> 4.0` - Template engine for views
- `listen ~> 3.8` - File watching for live reload
- `faye-websocket ~> 0.11` - WebSocket support for live updates

### Development Environment
The `/simulation/` directory provides a complete standalone environment that mimics TRMNL's plugin architecture:
- **Live Development Server**: WebSocket-powered live reload at `http://localhost:4567`
- **Multi-Layout Preview**: All 4 layouts displayed simultaneously with TRMNL device frames
- **Theme Testing**: Interactive color theme testing (white, black, mint, gray, wood)
- **Data Introspection**: Real-time data inspection panel for debugging

### TRMNL Framework Architecture
- **E-ink Optimization**: 1-bit rendering (black and white) with specialized CSS classes
- **Layout System**: Hierarchical layout with `view`, `layout`, `flex`, and `grid` systems
- **Responsive Design**: Layout-aware rendering with different content density based on display size
- **Component System**: Shared templates in `/lib/notion/views/shared/` for reusable elements

### Testing Approach
- **No Traditional Framework**: No RSpec, Jest, or conventional testing frameworks
- **Custom Test Scripts**: Plugin instantiation testing via `rake test`
- **Visual Validation**: Manual simulation verification through live preview
- **Live Development**: WebSocket reload for immediate feedback during development