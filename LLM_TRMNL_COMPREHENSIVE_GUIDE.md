Of course. Here is the recategorized and comprehensively structured guide for the TRMNL Framework, designed for clarity, with detailed explanations and examples.

# TRMNL Framework: A Comprehensive Guide for E-Ink Interfaces

## 1\. Introduction & Core Philosophy 📜

The **TRMNL Framework** is a specialized HTML and CSS framework designed for creating crisp, readable, and performant user interfaces on **1-bit e-ink displays**. It combines a utility-first CSS library with powerful JavaScript-driven enhancements to address the unique challenges of e-ink technology, such as low refresh rates and the need for perfect pixel alignment.

**Core Principles:**

  * **E-Ink First:** Every component and utility is optimized for black-and-white, non-backlit screens, prioritizing high contrast and clarity.
  * **Utility-First CSS:** Build complex layouts directly in your HTML using a rich set of low-level classes for spacing, typography, and color.
  * **JavaScript Enhancements:** A suite of JavaScript functions automatically applies advanced optimizations like pixel-perfect text rendering, dynamic font sizing, and intelligent content truncation after the page loads.
  * **Responsive by Design:** The framework is built around a system of containers (`views`) that adapt layouts to different screen sizes and orientations (e.g., full, half, quadrant).

-----

## 2\. Getting Started: Fundamental Concepts

Every TRMNL plugin is built from a set of core structural elements. Understanding these fundamentals is the key to building any layout.

### 2.1. The View Container: Your Canvas

All content must reside within a `view` container. This root element defines the overall size and boundaries of your plugin's interface.

  * `.view--full`: **800x480px** - The entire screen.
  * `.view--half_horizontal`: **800x240px** - The top or bottom half of the screen.
  * `.view--half_vertical`: **400x480px** - The left or right half of the screen.
  * `.view--quadrant`: **400x240px** - A quarter of the screen.

**Example:**

```html
<div class="view view--quadrant">
  </div>
```

\<br\>

### 2.2. Layout Systems: Structuring Your Content

TRMNL provides flexible systems for arranging elements inside a `view`.

#### **Hierarchical Layout (`layout`)**

The `layout` class is the primary container for arranging content in simple rows or columns.

| Class | Description |
| :--- | :--- |
| `layout--col` | Stacks children vertically. |
| `layout--row` | Arranges children horizontally. |
| `gap--large` | Adds significant space between children. |
| `gap--space-between`| Pushes children to the opposite ends of the container. |

**Example: A Vertical Layout**

```html
<div class="layout layout--col gap--large">
  <span>Item 1</span>
  <span>Item 2</span>
  <span>Item 3</span>
</div>
```

-----

#### **Flex System (`flex`)**

For more advanced alignment and distribution control, use the `flex` system.

| Class | Description |
| :--- | :--- |
| `flex--row` / `flex--col` | Sets the direction of items. |
| `flex--center` | Centers items on both axes. |
| `flex--start` / `flex--end` | Aligns items to the beginning or end. |
| `flex--between` / `flex--around` | Controls space distribution between items. |
| `stretch` | A child class that makes an element fill available space. |

**Example: Centered and Stretched Items**

```html
<div class="flex flex--col h--full">
  <div>This is at the top.</div>
  <div class="stretch">This item stretches to fill the middle space.</div>
  <div>This is at the bottom.</div>
</div>
```

-----

#### **Grid System (`grid`)**

The `grid` system is ideal for creating structured, multi-column layouts.

| Class | Description |
| :--- | :--- |
| `grid--cols-{1-6}` | Defines a grid with a fixed number of columns. |
| `col--span-{1-6}` | Makes a grid item span multiple columns. |
| `grid--rows-{1-6}` | Defines a grid with a fixed number of rows. |
| `row--span-{1-6}` | Makes a grid item span multiple rows. |

**Example: A 2x2 Grid**

```html
<div class="grid grid--cols-2 grid--rows-2">
  <div class="col--span-1 row--span-1">Cell 1</div>
  <div class="col--span-1 row--span-1">Cell 2</div>
  <div class="col--span-2 row--span-1">Cell 3 (spans two columns)</div>
</div>
```

\<br\>

### 2.3. Typography: Displaying Text ✍️

Typography is critical for readability on e-ink. TRMNL provides semantic classes to define the role and appearance of text.

#### **Value Classes (`value`)**

For primary data, like numbers, stock prices, or temperatures.

| Class | Description |
| :--- | :--- |
| `.value` | Default primary text. |
| `.value--{size}` | Modifiers like `small`, `large`, `xlarge`, `xxlarge`, `xxxlarge`. |
| `.value--tnums` | Enables **tabular numbers**, ensuring numbers align neatly in columns. |

**Example:**

```html
<span class="value value--large value--tnums">$1,250.50</span>
```

-----

#### **Label Classes (`label`)**

For secondary information, descriptions, and metadata.

| Class | Description |
| :--- | :--- |
| `.label` | Default secondary text. |
| `.label--small` | A smaller version of the label. |
| `.label--underline` | Adds a crisp, 1-bit underline. |
| `.label--gray-out` | Applies a dithered pattern to de-emphasize the text. |

**Example:**

```html
<span class="label">Last Updated</span>
<span class="label label--gray-out">Completed Task</span>
```

-----

#### **Other Text Classes**

  * **`.title`**: For section headings. Comes in `small` and `large` variants.
  * **`.description`**: For longer blocks of body text.

\<br\>

### 2.4. Common Layout Patterns

By combining the core concepts, you can create standardized, reusable patterns.

#### **The `item` Structure**

A universal pattern for list entries or metric displays. It separates metadata (like an index or icon) from the main content.

```html
<div class="item">
  <div class="meta">
    <span class="index">1</span>
  </div>
  <div class="content">
    <span class="value">$1,234</span>
    <span class="label">Revenue</span>
  </div>
</div>
```

-----

#### **The `title_bar`**

A standard footer for every plugin that identifies it.

```html
<div class="title_bar">
  <img class="image" src="/path/to/plugin-icon.svg" />
  <span class="title">Plugin Name</span>
  <span class="instance">Instance Name</span>
</div>
```

-----

## 3\. JavaScript-Powered Enhancements ✨

TRMNL's "magic" comes from its JavaScript attributes. These `data-*` attributes are hooks that tell the framework to apply complex optimizations automatically.

### 3.1. Pixel-Perfect Text Rendering

This is the **most important feature** for e-ink clarity. It prevents the blurry, anti-aliased text that plagues low-resolution displays.

  * **Attribute:** `data-pixel-perfect="true"`
  * **What it does:** The JS engine analyzes the text and wraps each line in a `<span>` with an exact integer pixel width, ensuring it aligns perfectly to the physical pixel grid.

**Example:**

```html
<p class="description" data-pixel-perfect="true">
  This long paragraph will be perfectly aligned to the e-ink pixel grid,
  ensuring every letter is sharp and readable.
</p>
```

\<br\>

### 3.2. Dynamic Text & Value Sizing

Automatically resizes text to fit its container, preventing ugly overflows.

  * **Attribute:** `data-fit-value="true"` or `data-value-fit="true"`
  * **What it does:** If text is too wide for its container, the JS will progressively shrink the font size (and adjust its weight for readability) until it fits.
  * **Constraint:** Use `data-value-fit-max-height="{pixels}"` to also limit the height.

**Example:**

```html
<div class="w--[150px]">
  <span class="value value--xxxlarge" data-fit-value="true">
    72°
  </span>
</div>
```

\<br\>

### 3.3. Intelligent Number Formatting

Formats large numbers into a compact, readable format, essential for small displays.

  * **Attribute:** `data-value-format="true"`
  * **What it does:** Abbreviates numbers (e.g., 1,234,000 becomes "1.2M"). It intelligently reduces precision if space is extremely limited.
  * **Localization:** Use `data-value-locale="{code}"` (e.g., `en-US`, `de-DE`) for region-appropriate formatting. Supports various currency symbols.

**Example:**

```html
<span class="value" data-value-format="true" data-value-locale="en-US">
  1234567
</span>
```

\<br\>

### 3.4. Content & List Management

Automatically manages long lists and content blocks to prevent them from overflowing their containers.

#### **Content Limiter**

For general content blocks.

  * **Attribute:** `data-content-limiter="true"`
  * **What it does:** If content exceeds a predefined height (based on the `view` size), it automatically applies `clamp` classes to truncate the text.
  * **Override:** Use `data-content-max-height="{pixels}"` for a custom height limit.

-----

#### **List Limiter**

For lists of `.item` elements.

  * **Attribute:** `data-list-limit="true"`
  * **What it does:** Hides list items that don't fit within the `data-list-max-height`.
  * **Modifiers:**
      * `data-list-max-columns="{1-3}"`: Wraps the list into multiple columns to fit more items.
      * `data-list-hidden-count="true"`: Displays an "And X more" indicator for hidden items.

**Example: A multi-column list that shows a hidden count**

```html
<div class="list"
     data-list-limit="true"
     data-list-max-height="150"
     data-list-max-columns="2"
     data-list-hidden-count="true">
  </div>
```

-----

## 4\. Utility Class Reference 🛠️

Here is a categorized reference of the most common utility classes available in TRMNL.

### 4.1. Sizing & Spacing

| Category | Prefix | Purpose | Example |
| :--- | :--- | :--- | :--- |
| **Width** | `w--` | Set width. Use scale (`{0-96}`) or pixels (`[{N}px]`). | `w--16` (64px), `w--[150px]` |
| **Height**| `h--` | Set height. Use scale (`{0-96}`) or pixels (`[{N}px]`). | `h--full`, `h--[200px]` |
| **Margin**| `m--`, `mx--`, `my--`, `mt--`, etc. | Set margin. Uses the size scale. | `mt--2` (8px top margin) |
| **Padding**| `p--`, `px--`, `py--`, `pt--`, etc. | Set padding. Uses the size scale. | `px--4` (16px horizontal padding) |
| **Gap** | `gap--` | Set the space between `flex` or `grid` children. | `gap--small`, `gap--large` |

\<br\>

### 4.2. Colors, Backgrounds & Borders

TRMNL uses a limited palette optimized for e-ink. Grays are achieved through **dithering patterns**, not actual gray colors.

| Category | Prefix / Class | Example |
| :--- | :--- | :--- |
| **Background Color** | `bg--{color}` | `bg--black`, `bg--gray-3` (a dither pattern) |
| **Text Color** | `text--{color}` | `text--white`, `text--gray-5` |
| **Borders** | `border`, `border--{color}`, `border--{style}` | `border border-black`, `border--dashed` |
| **Dividers** | `b-h-gray-{1-7}`, `b-v-gray-{1-7}` | `b-h-gray-5` (horizontal dithered line) |

\<br\>

### 4.3. E-Ink Specific Visuals

| Class | Purpose |
| :--- | :--- |
| **`image-dither`** | Applies a dithering filter to an `<img>` to simulate grayscale. |
| **`text-stroke`** | Adds a white or black outline to text, making it stand out on complex backgrounds. |
| **`image-stroke`** | Adds a white or black outline to an image. |

**Example: Stroked text on a dithered background**

```html
<div class="bg--gray-4 p--4">
  <span class="value text-stroke text-stroke--black">
    Standout Text
  </span>
</div>
```

-----

## 5\. Best Practices & Advanced Patterns

Follow these guidelines to create effective and performant TRMNL plugins.

### 5.1. The Golden Rules of E-Ink Design

1.  **Always Use `data-pixel-perfect="true"`:** Apply this to *all* text elements (`value`, `label`, `title`, `description`) to guarantee crispness.
2.  **Combine JS Attributes:** Don't be shy about combining attributes. A primary value should often have `data-pixel-perfect`, `data-fit-value`, and `data-value-format` all on the same element.
3.  **Use Dithering for Grays:** Never use CSS `opacity` or `rgba()`. Stick to the `bg--gray-{1-7}` classes for predictable, crisp patterns.
4.  **Embrace High Contrast:** Use `text--black` on `bg--white` (or vice-versa) whenever possible.
5.  **Disable Animations:** When integrating third-party libraries like charts, always find the option to disable animations (e.g., `animation: false`).

### 5.2. Responsive Design Strategies

Adapt your layouts based on the `view` size using conditional logic in your templating language.

**Strategy: Progressive Disclosure**
Show more information on larger views and less on smaller ones.

```erb
<%# ERB Example %>

<%# Set variables based on view size %>
<% if view_size == 'full' %>
  <% max_items = 10 %>
  <% show_description = true %>
<% elsif view_size == 'half_horizontal' %>
  <% max_items = 5 %>
  <% show_description = false %>
<% else %>
  <% max_items = 3 %>
  <% show_description = false %>
<% end %>

<%# Use variables to render the view %>
<div class="list" data-list-limit="true">
  <% items.first(max_items).each do |item| %>
    <div class="item">
      <span class="label" data-pixel-perfect="true"><%= item.name %></span>
      <% if show_description %>
        <span class="description" data-pixel-perfect="true">
          <%= item.description %>
        </span>
      <% end %>
    </div>
  <% end %>
</div>
```

### 5.3. Plugin-Specific Examples

  * **Stock Ticker:** Use `value--tnums` to align prices. Use `data-fit-value` to handle tickers with long names. Use conditional logic to switch between a 1-column and 2-column `grid` based on the number of stocks.
  * **Weather:** Use a large `value--xxxlarge` with `data-fit-value` for the main temperature. Use `grid` and `col--span` to create an asymmetrical layout with a large icon next to stacked forecast data.
  * **Calendar:** Use a multi-column `list` with `data-list-limit` and `data-list-hidden-count` to display events for the week, automatically truncating days with too many appointments.
  * **Todo List:** Use the `label--gray-out` class to visually strike through completed items. Separate "To Do" and "Completed" into two `layout--col` containers.