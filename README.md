# TRMNL Erb Development Environment + Notion TRMNL Plugin

A Ruby-based plugin for [TRMNL](https://usetrmnl.com) e-ink displays that beautifully renders Notion content. Display your Notion databases and pages on crisp, readable e-ink screens with optimized layouts and rich content support.

<img width="1842" height="1257" alt="image" src="https://github.com/user-attachments/assets/92f51b4e-9c28-43a8-b50e-4a57c2bf9533" />
<img width="1823" height="1273" alt="image" src="https://github.com/user-attachments/assets/6bd4fc69-81f3-4a3f-aed0-2b22c8302604" />
<img width="1833" height="1247" alt="image" src="https://github.com/user-attachments/assets/bd2a3fad-b142-4565-9e2a-1199aefdb545" />
<img width="1819" height="1268" alt="image" src="https://github.com/user-attachments/assets/31dc3e09-961c-440d-8abf-495db2e623e0" />


## Features

- **Dual Content Types**: Display both Notion databases and individual pages
- **Smart Property Display**: Enhanced emoji-based property organization and visual hierarchy
- **Mock Data**: Complete offline development with realistic sample data

## Development Environment Features

- **Multi-Layout Preview**: See all 4 TRMNL layouts simultaneously
- **Live Reload**: WebSocket-powered instant updates when files change

## Quick Start

### Prerequisites
- Ruby (see `simulation/Gemfile` for version requirements)
- A Notion workspace with content to display
- TRMNL device (for production use)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd trmnl_notion
   ```

2. **Install dependencies and setup environment**
   ```bash
   rake install
   ```
   This automatically installs Ruby dependencies and creates a `.env` file for development configuration.

3. **Configure Notion credentials** (optional - for testing with real data)
   ```bash
   # Edit simulation/.env with your Notion credentials
   # See Development Configuration section below for details
   ```

4. **Start development server**
   ```bash
   rake server
   ```

5. **View in browser**
   Open [http://localhost:4567](http://localhost:4567) to see all layouts with live preview

## Notion Setup

Before using the plugin, you need to set up Notion integration. **See [lib/notion/NOTION_SETUP.md](lib/notion/NOTION_SETUP.md)** for complete step-by-step instructions including:

1. Create Notion integration at [notion.so/my-integrations](https://www.notion.so/my-integrations)
2. Copy your integration token (starts with `secret_`)
3. Share your target database/page with the integration
4. Copy the database/page ID from the URL
5. Configure the TRMNL plugin with these credentials

## Plugin Configuration

The plugin supports 12 configuration options defined in `lib/notion/form_fields.yaml`:

| Setting | Type | Description |
|---------|------|-------------|
| **Integration Token** | Text (encrypted) | Your Notion integration token (starts with "secret_") |
| **Content Type** | Select | Choose "Database" or "Page" |
| **Database ID** | Text | Notion database ID (required for database type) |
| **Page ID** | Text | Notion page ID (required for page type) |
| **Display Title** | Text | Custom title for your TRMNL display (optional) |
| **Item Limit** | Number | Maximum items to show (databases only, default: 8) |
| **Image Height** | Number | Maximum height for images in pixels (optional, default: 120) |
| **Status Field** | Text | Property to use as primary status indicator (optional, databases only) |
| **Sort By** | Text | Primary property to sort by (optional, databases only) |
| **Sort Direction** | Select | Sort direction for primary property (ascending/descending, default: descending) |
| **Secondary Sort By** | Text | Secondary property for nested sorting (optional, databases only) |
| **Secondary Sort Direction** | Select | Sort direction for secondary property (ascending/descending, default: ascending) |
| **Multi-Column Display** | Select | Display items in multiple columns (1 or 2 columns, default: 2, databases only) |

## Development

### Development Configuration

The `rake install` command automatically creates a `.env` file in the `simulation/` directory for development configuration. To test with real Notion data, edit this file with your credentials.

**Note**: The `.env` file is optional. The plugin includes comprehensive mock data for development without requiring Notion API access. Use `.env` only when you want to test with your actual Notion content.

```env
# Notion API Configuration (for development testing)
NOTION_INTEGRATION_TOKEN=secret_your_integration_token_here
NOTION_DATABASE_ID=your_database_id_here
NOTION_PAGE_ID=your_page_id_here
```

### Available Commands

```bash
rake install         # Install Ruby dependencies
rake server          # Start development server at http://localhost:4567
rake test            # Run plugin setup test
rake rubocop         # Run style checks
rake rubocop_fix     # Auto-fix style issues
rake help            # Show available tasks
```

### For Developers

**See [CLAUDE.md](CLAUDE.md)** for comprehensive development guidance including:
**See [TRMNL_COMPREHENSIVE_GUIDE.md](TRMNL_COMPREHENSIVE_GUIDE.md)** for a complete reference of TRMNL CSS classes and design patterns optimized for e-ink displays.


## Testing

The project uses a custom testing approach optimized for visual validation:

- **Custom Scripts**: Plugin instantiation testing via `rake test`
- **Visual Validation**: Live preview with all layouts simultaneously
- **Manual Testing**: Interactive development environment for immediate feedback
- **No Traditional Framework**: No RSpec or Jest - focus on visual correctness for e-ink displays

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Documentation

- **Setup Instructions**: [lib/notion/NOTION_SETUP.md](lib/notion/NOTION_SETUP.md) - Complete Notion integration setup
- **Development Guide**: [CLAUDE.md](CLAUDE.md) - Technical architecture and development guidance  
- **TRMNL Framework**: [TRMNL_COMPREHENSIVE_GUIDE.md](TRMNL_COMPREHENSIVE_GUIDE.md) - Complete CSS class reference and e-ink design patterns

## Support

- **Setup Issues**: See troubleshooting section in [lib/notion/NOTION_SETUP.md](lib/notion/NOTION_SETUP.md)
- **Development Help**: Check [CLAUDE.md](CLAUDE.md) for technical guidance
- **Framework Reference**: See [TRMNL_COMPREHENSIVE_GUIDE.md](TRMNL_COMPREHENSIVE_GUIDE.md) for CSS classes and layout patterns

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run `rake test` and `rake rubocop` to validate
5. Test in the development environment at `http://localhost:4567`
6. Submit a pull request

The development environment makes it easy to see changes instantly across all four TRMNL layout formats.
