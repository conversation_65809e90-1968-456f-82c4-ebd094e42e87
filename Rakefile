require "bundler/setup"

# Set up task to work from simulation directory
def in_simulation_dir(&)
  Dir.chdir("simulation", &)
end

desc "Install Ruby dependencies and setup development environment"
task :install do
  in_simulation_dir do
    puts "Installing Ruby dependencies..."
    system("bundle install") || abort("Failed to install dependencies")

    # Copy .env.example to .env if it doesn't exist
    if File.exist?(".env")
      puts "✅ .env file already exists"
    elsif File.exist?(".env.example")
      puts "Setting up development environment configuration..."
      system("cp .env.example .env")
      puts "✅ Created .env file from .env.example"
      puts "📝 Edit simulation/.env with your Notion credentials to test with real data"
      puts "   (Optional - plugin includes mock data for development)"
    else
      puts "⚠️  Warning: .env.example file not found"
    end

    puts ""
    puts "🎉 Installation complete!"
    puts "💡 Run 'rake server' to start the development server"
  end
end

desc "Start the development server"
task :server do
  in_simulation_dir do
    puts "Starting TRMNL Plugin Development Server..."
    exec("ruby server.rb")
  end
end

desc "Run plugin setup test"
task :test do
  in_simulation_dir do
    puts "Running plugin setup test..."
    system("ruby test_setup.rb") || abort("Test failed")
  end
end

desc "Run RuboCop style checks"
task :rubocop do
  in_simulation_dir do
    puts "Running RuboCop style checks..."
    system("bundle exec rubocop -c ../.rubocop.yml ..") || abort("RuboCop found style issues")
  end
end

desc "Auto-fix RuboCop style issues"
task :rubocop_fix do
  in_simulation_dir do
    puts "Auto-fixing RuboCop style issues..."
    system("bundle exec rubocop -A -c ../.rubocop.yml ..") || abort("RuboCop auto-fix failed")
  end
end

desc "Check RuboCop configuration"
task :rubocop_config do
  in_simulation_dir do
    puts "Checking RuboCop configuration..."
    system("bundle exec rubocop --show-config ..")
  end
end

desc "Export all view templates to HTML files"
task :export_views do
  in_simulation_dir do
    puts "Exporting all view templates to HTML files..."
    system("ruby export_views.rb") || abort("Export failed")
    puts "✅ Views exported successfully to exports/ directory"
  end
end

desc "Format all erb files"
task :format_erb do
  in_simulation_dir do
    puts "Formatting all erb files..."
    system("bundle exec erb_lint --lint-all -a ..") || abort("Formatting failed")
  end
end

desc "Show available tasks"
task :help do
  puts "Available Rake tasks:"
  puts "  rake install      - Install dependencies and setup development environment"
  puts "  rake server       - Start development server"
  puts "  rake test         - Run plugin setup test"
  puts "  rake rubocop      - Run style checks"
  puts "  rake rubocop_fix  - Auto-fix style issues"
  puts "  rake export_views - Export all view templates to HTML files"
  puts "  rake format_erb   - Format all erb files"
  puts "  rake help         - Show this help message"
end

# Default task
task default: :help