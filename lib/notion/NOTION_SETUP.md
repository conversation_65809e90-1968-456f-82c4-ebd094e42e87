# Notion Plugin Setup Guide

## Simple Integration with Internal Integration Token

This plugin uses **Notion Internal Integrations** - the easiest way to connect to Notion API without OAuth complexity.

## Step 1: Create Notion Integration

1. Go to [notion.so/my-integrations](https://www.notion.so/my-integrations)
2. Click **"New Integration"**
3. Choose **"Internal Integration"** (workspace-specific)
4. Give it a name (e.g., "TRMNL Display")
5. Select your workspace
6. Click **"Submit"**

## Step 2: Get Integration Token

1. After creating the integration, you'll see your **Integration Token**
2. It starts with `secret_` followed by a long string
3. **Keep this token secure** - treat it like a password
4. Copy the token for the next step

## Step 3: Share Content with Integration

**Important:** Your integration needs permission to access specific pages/databases.

### For Databases:
1. Open the database in Notion
2. Click the **"••• More"** menu (top right)
3. Scroll down to **"Add connections"**
4. Search for your integration name
5. Click to add it

### For Pages:
1. Open the page in Notion
2. Click the **"••• More"** menu (top right)
3. Scroll down to **"Add connections"**
4. Search for your integration name
5. Click to add it

## Step 4: Get Database/Page IDs

### Database ID:
1. Open your database in Notion
2. Copy the URL from your browser
3. The database ID is the long string between the last `/` and `?`
   ```
   https://notion.so/myworkspace/My-Database-123456789abcdef123456789abcdef12?v=...
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                              This is your database ID
   ```

### Page ID:
1. Open your page in Notion
2. Copy the URL from your browser
3. The page ID is the long string at the end after the last `-`
   ```
   https://notion.so/myworkspace/My-Page-Title-123456789abcdef123456789abcdef12
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                              This is your page ID
   ```

## Step 5: Configure TRMNL Plugin

1. **Integration Token**: Paste your `secret_xxxxx` token
2. **Display Type**: Choose "Database" or "Page"
3. **Database ID** (if Database): Paste the database ID
4. **Page ID** (if Page): Paste the page ID
5. Configure other options as desired

## Example Configuration

```yaml
Integration Token: secret_AbCdEf123456789XyZ987654321
Content Type: Database
Database ID: 123456789abcdef123456789abcdef12
Display Title: My Project Dashboard
Item Limit: 8
```

## Troubleshooting

### "No items found" or empty display:
1. ✅ Verify integration token is correct
2. ✅ Make sure you **shared** the database/page with your integration
3. ✅ Check that database/page ID is correct
4. ✅ Ensure database/page isn't empty

### "Access denied" errors:
1. ✅ Integration must be shared with the specific content
2. ✅ You must be a workspace admin to create integrations
3. ✅ Token must not be expired or revoked

### Database shows no properties:
1. ✅ Enable "Show Properties" in plugin settings
2. ✅ Database must have actual property columns
3. ✅ Properties must have values in the database items

## Special Field Configuration

The plugin allows you to configure which of your Notion properties should serve specific functions in the display. This gives you complete control over how your data is presented.

### Special Field Types

| Field Type | Purpose | Display Effect | Configuration Field |
|------------|---------|----------------|-------------------|
| **Status Field** | Primary status/priority indicator | Prominent badge next to title | `Status Field` |
| **Date Field** | Primary date/deadline | Underlined styling for emphasis | `Date Field` |
| **Person Field** | Primary assignee/owner | Standard styling, person-focused | `Person Field` |
| **Progress Field** | Progress/completion tracking | Standard styling, progress-focused | `Progress Field` |

### How Special Fields Work

```
┌─────────────────────────────────────────┐
│ 📋 Website Redesign Project             │
│ 🏷️ High                    ← Status Field │
│                                         │
│ 📅 Jan 15, 2025    👤 Sarah Chen       │
│    ↑ Date Field         ↑ Person Field  │
│                                         │
│ 📊 65%              Other Properties    │
│ ↑ Progress Field                        │
└─────────────────────────────────────────┘
```

### Configuration Examples

#### Example 1: Project Management Database
Your Notion database has these properties:
- `Project Status` (Select: Planning, In Progress, Review, Done)
- `Priority` (Select: Low, Medium, High, Critical)
- `Deadline` (Date)
- `Project Manager` (Person)
- `Completion %` (Text: 0%, 25%, 50%, 75%, 100%)

**Configuration:**
- **Status Field**: `Priority` (will show as prominent badge)
- **Date Field**: `Deadline` (will show with underlined styling)
- **Person Field**: `Project Manager` (will display person name)
- **Progress Field**: `Completion %` (will show progress indicator)

#### Example 2: Task Management Database
Your Notion database has these properties:
- `Task Status` (Select: Todo, In Progress, Done)
- `Due Date` (Date)
- `Assignee` (Person)
- `Effort` (Select: Small, Medium, Large)

**Configuration:**
- **Status Field**: `Task Status`
- **Date Field**: `Due Date`
- **Person Field**: `Assignee`
- **Progress Field**: `Effort`

### Field Behavior Details

#### 🏷️ **Status Field**
- **Purpose**: Primary status/priority indicator
- **Display**: Shows as a **prominent badge** next to the item title
- **Styling**: Most visible element, acts as the main status indicator
- **Behavior**: 
  - Appears in all layouts (full, half, quadrant)
  - Gets priority positioning near the title
  - Hidden from the regular property list to avoid duplication
- **Best for**: Status columns, Priority fields, State indicators

#### 📅 **Date Field**
- **Purpose**: Primary date/deadline information
- **Display**: Shows with **underlined styling** to emphasize time-sensitive info
- **Styling**: Distinctive underline makes dates stand out
- **Behavior**: 
  - Appears in the property list with special formatting
  - Formats dates as "Jan 15, 2025" for readability
  - Helps users quickly identify deadlines
- **Best for**: Due dates, Deadlines, Start dates, Created dates

#### 👤 **Person Field**
- **Purpose**: Primary assignee/ownership information
- **Display**: Shows as a **regular property** but semantically identified as person-related
- **Styling**: Standard label styling but processed as person data
- **Behavior**: 
  - Displays people names clearly
  - Handles multiple people (comma-separated)
  - Easy to identify who's responsible
- **Best for**: Assignee, Owner, Responsible person, Author, Creator

#### 📊 **Progress Field**
- **Purpose**: Progress/completion tracking
- **Display**: Shows as a **regular property** but semantically identified as progress
- **Styling**: Standard label styling but conceptually represents progress
- **Behavior**: 
  - Displays progress values (percentages, completion states)
  - Can handle text-based progress indicators
  - Helps track completion status
- **Best for**: Progress percentages, Completion status, % Complete fields

### Configuration Tips

1. **Optional Configuration**: All special fields are optional - leave blank if not needed
2. **Exact Names**: Use the exact property name as it appears in your Notion database
3. **Case Sensitive**: Property names must match exactly (case-sensitive)
4. **Flexibility**: You can assign any property to any field type
5. **Fallback**: Properties not assigned to special fields display with automatic styling based on their Notion type

### Default Property Display

Properties not assigned to special fields will display with these defaults:
- **Select/Status** properties → Badge styling
- **Date** properties → Underlined date formatting  
- **Multi-select** properties → Chip styling
- **Number** properties → Numeric display
- **People** properties → Person formatting
- **Text** properties → Standard styling

### Benefits

✅ **User Control**: Choose which properties get special treatment  
✅ **Visual Hierarchy**: Important properties stand out automatically  
✅ **Flexible**: Works with any database structure or naming convention  
✅ **Consistent**: Same field types always display the same way  
✅ **Responsive**: Adapts to different display sizes

## API Reference

The plugin uses these Notion API endpoints:
- `GET /v1/databases/{id}/query` - Fetch database items
- `GET /v1/pages/{id}` - Fetch page metadata  
- `GET /v1/blocks/{id}/children` - Fetch page content blocks

Authentication: `Bearer {integration_token}`