module Notion
  module Configuration
    FORM_DEFAULTS = begin
      require "yaml"
      form_fields = YAML.load_file(File.join(File.dirname(__FILE__), "../form_fields.yaml"))
      form_fields.each_with_object({}) do |field, defaults|
        defaults[field["keyname"]] = field["default"] if field["default"]
      end
    rescue StandardError => e
      puts "Warning: Could not load form_fields.yaml defaults: #{e.message}"
      {}
    end.freeze

    def self.load_defaults_into(settings)
      FORM_DEFAULTS.each do |key, default_value|
        settings[key] ||= default_value
      end
    end

    def self.setup_environment_variables(settings)
      return unless ENV["NOTION_TOKEN"]

      settings["integration_token"] ||= ENV.fetch("NOTION_TOKEN", nil)
      settings["database_id"] ||= ENV["NOTION_DATABASE_ID"] if ENV["NOTION_DATABASE_ID"]
      settings["page_id"] ||= ENV["NOTION_PAGE_ID"] if ENV["NOTION_PAGE_ID"]
    end
  end

  # Merged Utilities module
  module Utilities
    def present?(value)
      value && !value.empty?
    end

    def log_error(message)
      puts "⚠️  [Notion API] #{message}"
    end

    def safe_fetch(response, *keys)
      return {} unless response&.success?

      data = response.parsed_response
      keys.reduce(data) { |obj, key| obj&.dig(key) } || {}
    end
  end
end