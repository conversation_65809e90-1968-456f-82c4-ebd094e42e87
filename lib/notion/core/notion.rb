require "httparty"
require "date"
require_relative "../services/data_provider"
require_relative "../data/mock_data"
require_relative "../helpers/notion_helpers"
require_relative "configuration"

module Plugins
  class Notion < Base
    include ::Notion::Helpers

    def initialize(plugin_settings, params = {})
      super
      load_form_defaults
      setup_environment_variables
    end

    def locals
      display_type = settings["display_type"]

      case display_type
      when "database"
        {
          items: data_provider.database_items,
          title: data_provider.database_title,
          display_type: "database",
          max_items_for_layout: settings["max_items"].to_i,
          multi_column_display: settings["multi_column_display"],
          helpers: self
        }
      when "page"
        {
          content: data_provider.page_content,
          title: data_provider.page_title,
          display_type: "page",
          max_items_for_layout: 9999,
          multi_column_display: settings["multi_column_display"],
          helpers: self
        }
      else
        { error: "Invalid display type", helpers: self }
      end
    end

    class << self
      def mock_variants
        ::Notion::MockData.simulation_variants
      end
    end

    def image_height
      settings["image_height"].to_i
    end

    private

    def load_form_defaults
      ::Notion::Configuration.load_defaults_into(@settings)
    end

    def setup_environment_variables
      ::Notion::Configuration.setup_environment_variables(@settings)
    end

    def data_provider
      @data_provider ||= ::Notion::DataProvider.new(settings, settings["integration_token"])
    end
  end
end