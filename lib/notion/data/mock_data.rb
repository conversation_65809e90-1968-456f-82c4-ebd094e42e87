require_relative "mock_data_loader"
require_relative "../core/configuration"
require_relative "../services/response_parser"

module Notion
  class MockData
    # Simplified configuration using the new loader
    MOCK_CONFIG = {
      databases: {
        "projects_db" => { title: "Active Projects", loader: :load_projects },
        "tasks_db" => { title: "Task List", loader: :load_tasks }
      },
      pages: {
        "company_wiki" => { title: "Company Wiki", loader: :wiki_content },
        "project_brief" => { title: "Project Brief", loader: :project_brief_content }
      }
    }.freeze

    class << self
      def database_items_for(database_id)
        config = MOCK_CONFIG[:databases][database_id]
        return [{ title: "No Data Available", properties: {}, url: "", last_edited: "" }] unless config

        MockDataLoader.send(config[:loader])
      end

      def page_content_for(page_id)
        config = MOCK_CONFIG[:pages][page_id]
        return { blocks: [{ type: "paragraph", text: "No content available for this page ID" }], properties: [] } unless config

        MockDataLoader.send(config[:loader])
      end

      def database_title_for(database_id)
        MOCK_CONFIG.dig(:databases, database_id, :title) || "Database"
      end

      def page_title_for(page_id)
        MOCK_CONFIG.dig(:pages, page_id, :title) || "Page"
      end

      def simulation_variants
        VariantGenerator.simulation_variants
      end
    end
  end

  # Merged VariantGenerator module
  module VariantGenerator
    include ResponseParser

    def self.simulation_variants
      if ENV["NOTION_TOKEN"]
        real_variants = {}
        base_settings = Configuration::FORM_DEFAULTS.dup

        if ENV["NOTION_DATABASE_ID"]
          database_title = fetch_real_title("database", ENV["NOTION_DATABASE_ID"])
          real_variants["real_database"] = base_settings.merge({
                                                                 "database_id" => ENV["NOTION_DATABASE_ID"],
                                                                 "instance_name" => database_title || "Real Database",
                                                                 "plugin_name" => "notion"
                                                               })
        end

        if ENV["NOTION_PAGE_ID"]
          page_title = fetch_real_title("page", ENV["NOTION_PAGE_ID"])
          real_variants["real_page"] = base_settings.merge({
                                                             "display_type" => "page",
                                                             "page_id" => ENV["NOTION_PAGE_ID"],
                                                             "instance_name" => page_title || "Real Page",
                                                             "plugin_name" => "notion"
                                                           })
        end

        return real_variants unless real_variants.empty?
      end

      mock_variants
    end

    def self.mock_variants
      base_settings = Configuration::FORM_DEFAULTS.dup

      {
        "database_projects" => base_settings.merge({
                                                     "database_id" => "projects_db",
                                                     "instance_name" => "Active Projects",
                                                     "plugin_name" => "notion"
                                                   }),
        "database_tasks" => base_settings.merge({
                                                  "database_id" => "tasks_db",
                                                  "instance_name" => "Task List",
                                                  "max_items" => 6,
                                                  "plugin_name" => "notion"
                                                }),
        "database_custom_fields" => base_settings.merge({
                                                          "database_id" => "projects_db",
                                                          "instance_name" => "Custom Field Demo",
                                                          "max_items" => 6,
                                                          "status_field" => "Priority",
                                                          "plugin_name" => "notion"
                                                        }),
        "database_sorted_by_priority" => base_settings.merge({
                                                               "database_id" => "projects_db",
                                                               "instance_name" => "Priority Sorted",
                                                               "max_items" => 6,
                                                               "sort_property" => "Priority",
                                                               "sort_direction" => "descending",
                                                               "secondary_sort_property" => "Due Date",
                                                               "secondary_sort_direction" => "ascending",
                                                               "plugin_name" => "notion"
                                                             }),
        "page_wiki" => base_settings.merge({
                                             "display_type" => "page",
                                             "page_id" => "company_wiki",
                                             "instance_name" => "Company Wiki",
                                             "plugin_name" => "notion"
                                           }),
        "page_project_brief" => base_settings.merge({
                                                      "display_type" => "page",
                                                      "page_id" => "project_brief",
                                                      "instance_name" => "Project Brief",
                                                      "plugin_name" => "notion"
                                                    })
      }
    end

    def self.fetch_real_title(type, id)
      return nil unless ENV["NOTION_TOKEN"]

      require "httparty"

      begin
        url = case type
              when "database"
                "https://api.notion.com/v1/databases/#{id}"
              when "page"
                "https://api.notion.com/v1/pages/#{id}"
              else
                return nil
              end

        response = HTTParty.get(url,
                                headers: {
                                  "Authorization" => "Bearer #{ENV.fetch('NOTION_TOKEN', nil)}",
                                  "Notion-Version" => "2022-06-28"
                                },
                                timeout: 5)

        if response.code == 200
          data = response.parsed_response
          extract_title_from_response(data, type)
        end
      rescue StandardError => e
        puts "⚠️  Could not fetch real #{type} title: #{e.message}"
        nil
      end
    end

    def self.extract_title_from_response(data, type)
      parser = Object.new
      parser.extend(ResponseParser)
      parser.extract_title_from_response(data, type)
    end
  end
end