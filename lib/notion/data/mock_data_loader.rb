module Notion
  class <PERSON>ck<PERSON><PERSON><PERSON>oa<PERSON>
    def self.load_projects
      projects_data
    end

    def self.load_tasks
      [
        build_task("Review pull request #423", "Todo", "High", "John <PERSON>", "2h", %w[Frontend Review]),
        build_task("Update documentation for API v3", "In Progress", "Medium", "<PERSON>", "4h", %w[Documentation API]),
        build_task("Fix CSS layout bug in Safari", "Done", "Critical", "<PERSON>", "3h", %w[Bug CSS Safari]),
        build_task("Implement user authentication flow", "Todo", "High", "Alex Kim", "8h", %w[Backend Security])
      ]
    end

    def self.wiki_content
      {
        blocks: [
          { type: "heading_1", text: "Company Wiki" },
          { type: "paragraph", text: "Welcome to our company knowledge base. Here you'll find everything you need to know about our processes, tools, and culture." },
          { type: "heading_2", text: "Quick Links" },
          { type: "bulleted_list_item", text: "Engineering Guidelines" },
          { type: "bulleted_list_item", text: "Design System Documentation" },
          { type: "bulleted_list_item", text: "HR Policies" },
          { type: "heading_2", text: "Recent Updates" },
          { type: "paragraph", text: "The engineering team has updated the deployment process. Please review the new guidelines before your next release." },
          { type: "callout", icon: "⚠️", text: "All team members must complete security training by January 31, 2025." },
          { type: "quote", text: "Innovation distinguishes between a leader and a follower. - Steve Jobs" },
          { type: "to_do", checked: true, text: "Q4 Planning Meeting - Completed" },
          { type: "to_do", checked: false, text: "Annual Review Process - Due Jan 31" },
          { type: "heading_2", text: "Development Standards" },
          { type: "paragraph", text: "All code must pass CI/CD pipeline before merging. Follow the coding standards document for style guidelines." },
          { type: "bulleted_list_item", text: "Run tests locally before committing" },
          { type: "bulleted_list_item", text: "Use meaningful commit messages" },
          { type: "bulleted_list_item", text: "Request code reviews for all changes" }
        ],
        properties: []
      }
    end

    def self.project_brief_content
      {
        blocks: [
          { type: "heading_1", text: "Website Redesign Project Brief" },
          { type: "image", image_url: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=900&h=600", caption: "Current website mockup for reference" },
          { type: "heading_2", text: "Executive Summary" },
          { type: "paragraph", text: "Our current website needs a complete redesign to improve user experience, modernize the visual design, and increase conversion rates." },
          { type: "heading_2", text: "Goals" },
          { type: "numbered_list_item", text: "Increase conversion rate by 25%" },
          { type: "numbered_list_item", text: "Reduce page load time to under 2 seconds" },
          { type: "numbered_list_item", text: "Improve mobile experience" },
          { type: "numbered_list_item", text: "Implement new brand guidelines" },
          { type: "heading_2", text: "Timeline & Milestones" },
          { type: "to_do", checked: true, text: "Discovery phase - Completed" },
          { type: "to_do", checked: true, text: "Wireframes and mockups - Completed" },
          { type: "to_do", checked: false, text: "Frontend development - In Progress" },
          { type: "to_do", checked: false, text: "Testing and QA - Pending" },
          { type: "to_do", checked: false, text: "Launch preparation - Pending" }
        ],
        properties: []
      }
    end

    def self.projects_data
      [
        {
          title: "Website Redesign Project",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "In Progress" } },
            "Priority" => { "type" => "select", "select" => { "name" => "High" } },
            "Due Date" => { "type" => "date", "date" => { "start" => "2025-01-15" } },
            "Team" => { "type" => "multi_select", "multi_select" => [{ "name" => "Design" }, { "name" => "Frontend" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "65%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$45K" }] },
            "Owner" => { "type" => "people", "people" => [{ "name" => "Sarah Chen" }] },
            "Notes" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "UI mockups completed, development in progress" }] }
          },
          url: "https://notion.so/website-redesign",
          last_edited: "Jan 10, 2025 14:30"
        },
        {
          title: "Q1 Marketing Campaign",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "Planning" } },
            "Priority" => { "type" => "select", "select" => { "name" => "Medium" } },
            "Due Date" => { "type" => "date", "date" => { "start" => "2025-02-01" } },
            "Team" => { "type" => "multi_select", "multi_select" => [{ "name" => "Marketing" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "25%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$25K" }] },
            "Owner" => { "type" => "people", "people" => [{ "name" => "Mike Johnson" }] },
            "Campaign Type" => { "type" => "select", "select" => { "name" => "Digital" } },
            "Target Audience" => { "type" => "multi_select", "multi_select" => [{ "name" => "B2B" }, { "name" => "Enterprise" }] }
          },
          url: "https://notion.so/q1-marketing",
          last_edited: "Jan 9, 2025 09:15"
        },
        {
          title: "Mobile App v2.0",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "Development" } },
            "Priority" => { "type" => "select", "select" => { "name" => "High" } },
            "Deadline" => { "type" => "date", "date" => { "start" => "2025-03-01" } },
            "Skills Required" => { "type" => "multi_select", "multi_select" => [{ "name" => "Mobile" }, { "name" => "Backend" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "40%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$120K" }] },
            "Lead Developer" => { "type" => "people", "people" => [{ "name" => "Alex Kim" }] },
            "Platform" => { "type" => "multi_select", "multi_select" => [{ "name" => "iOS" }, { "name" => "Android" }] }
          },
          url: "https://notion.so/mobile-app-v2",
          last_edited: "Jan 10, 2025 16:45"
        },
        {
          title: "Security Audit & Compliance",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "In Progress" } },
            "Priority" => { "type" => "select", "select" => { "name" => "Critical" } },
            "Deadline" => { "type" => "date", "date" => { "start" => "2025-01-25" } },
            "Teams" => { "type" => "multi_select", "multi_select" => [{ "name" => "Security" }, { "name" => "Legal" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "80%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$30K" }] },
            "Lead" => { "type" => "people", "people" => [{ "name" => "Lisa Zhang" }] },
            "Compliance Type" => { "type" => "select", "select" => { "name" => "SOC2" } }
          },
          url: "https://notion.so/security-audit",
          last_edited: "Jan 11, 2025 10:15"
        },
                {
          title: "Website Redesign Project",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "In Progress" } },
            "Priority" => { "type" => "select", "select" => { "name" => "High" } },
            "Due Date" => { "type" => "date", "date" => { "start" => "2025-01-15" } },
            "Team" => { "type" => "multi_select", "multi_select" => [{ "name" => "Design" }, { "name" => "Frontend" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "65%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$45K" }] },
            "Owner" => { "type" => "people", "people" => [{ "name" => "Sarah Chen" }] },
            "Notes" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "UI mockups completed, development in progress" }] }
          },
          url: "https://notion.so/website-redesign",
          last_edited: "Jan 10, 2025 14:30"
        },
        {
          title: "Q1 Marketing Campaign",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "Planning" } },
            "Priority" => { "type" => "select", "select" => { "name" => "Medium" } },
            "Due Date" => { "type" => "date", "date" => { "start" => "2025-02-01" } },
            "Team" => { "type" => "multi_select", "multi_select" => [{ "name" => "Marketing" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "25%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$25K" }] },
            "Owner" => { "type" => "people", "people" => [{ "name" => "Mike Johnson" }] },
            "Campaign Type" => { "type" => "select", "select" => { "name" => "Digital" } },
            "Target Audience" => { "type" => "multi_select", "multi_select" => [{ "name" => "B2B" }, { "name" => "Enterprise" }] }
          },
          url: "https://notion.so/q1-marketing",
          last_edited: "Jan 9, 2025 09:15"
        },
        {
          title: "Mobile App v2.0",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "Development" } },
            "Priority" => { "type" => "select", "select" => { "name" => "High" } },
            "Deadline" => { "type" => "date", "date" => { "start" => "2025-03-01" } },
            "Skills Required" => { "type" => "multi_select", "multi_select" => [{ "name" => "Mobile" }, { "name" => "Backend" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "40%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$120K" }] },
            "Lead Developer" => { "type" => "people", "people" => [{ "name" => "Alex Kim" }] },
            "Platform" => { "type" => "multi_select", "multi_select" => [{ "name" => "iOS" }, { "name" => "Android" }] }
          },
          url: "https://notion.so/mobile-app-v2",
          last_edited: "Jan 10, 2025 16:45"
        },
        {
          title: "Security Audit & Compliance",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "In Progress" } },
            "Priority" => { "type" => "select", "select" => { "name" => "Critical" } },
            "Deadline" => { "type" => "date", "date" => { "start" => "2025-01-25" } },
            "Teams" => { "type" => "multi_select", "multi_select" => [{ "name" => "Security" }, { "name" => "Legal" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "80%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$30K" }] },
            "Lead" => { "type" => "people", "people" => [{ "name" => "Lisa Zhang" }] },
            "Compliance Type" => { "type" => "select", "select" => { "name" => "SOC2" } }
          },
          url: "https://notion.so/security-audit",
          last_edited: "Jan 11, 2025 10:15"
        },
                {
          title: "Website Redesign Project",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "In Progress" } },
            "Priority" => { "type" => "select", "select" => { "name" => "High" } },
            "Due Date" => { "type" => "date", "date" => { "start" => "2025-01-15" } },
            "Team" => { "type" => "multi_select", "multi_select" => [{ "name" => "Design" }, { "name" => "Frontend" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "65%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$45K" }] },
            "Owner" => { "type" => "people", "people" => [{ "name" => "Sarah Chen" }] },
            "Notes" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "UI mockups completed, development in progress" }] }
          },
          url: "https://notion.so/website-redesign",
          last_edited: "Jan 10, 2025 14:30"
        },
        {
          title: "Q1 Marketing Campaign",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "Planning" } },
            "Priority" => { "type" => "select", "select" => { "name" => "Medium" } },
            "Due Date" => { "type" => "date", "date" => { "start" => "2025-02-01" } },
            "Team" => { "type" => "multi_select", "multi_select" => [{ "name" => "Marketing" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "25%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$25K" }] },
            "Owner" => { "type" => "people", "people" => [{ "name" => "Mike Johnson" }] },
            "Campaign Type" => { "type" => "select", "select" => { "name" => "Digital" } },
            "Target Audience" => { "type" => "multi_select", "multi_select" => [{ "name" => "B2B" }, { "name" => "Enterprise" }] }
          },
          url: "https://notion.so/q1-marketing",
          last_edited: "Jan 9, 2025 09:15"
        },
        {
          title: "Mobile App v2.0",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "Development" } },
            "Priority" => { "type" => "select", "select" => { "name" => "High" } },
            "Deadline" => { "type" => "date", "date" => { "start" => "2025-03-01" } },
            "Skills Required" => { "type" => "multi_select", "multi_select" => [{ "name" => "Mobile" }, { "name" => "Backend" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "40%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$120K" }] },
            "Lead Developer" => { "type" => "people", "people" => [{ "name" => "Alex Kim" }] },
            "Platform" => { "type" => "multi_select", "multi_select" => [{ "name" => "iOS" }, { "name" => "Android" }] }
          },
          url: "https://notion.so/mobile-app-v2",
          last_edited: "Jan 10, 2025 16:45"
        },
        {
          title: "Security Audit & Compliance",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "In Progress" } },
            "Priority" => { "type" => "select", "select" => { "name" => "Critical" } },
            "Deadline" => { "type" => "date", "date" => { "start" => "2025-01-25" } },
            "Teams" => { "type" => "multi_select", "multi_select" => [{ "name" => "Security" }, { "name" => "Legal" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "80%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$30K" }] },
            "Lead" => { "type" => "people", "people" => [{ "name" => "Lisa Zhang" }] },
            "Compliance Type" => { "type" => "select", "select" => { "name" => "SOC2" } }
          },
          url: "https://notion.so/security-audit",
          last_edited: "Jan 11, 2025 10:15"
        },
                {
          title: "Website Redesign Project",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "In Progress" } },
            "Priority" => { "type" => "select", "select" => { "name" => "High" } },
            "Due Date" => { "type" => "date", "date" => { "start" => "2025-01-15" } },
            "Team" => { "type" => "multi_select", "multi_select" => [{ "name" => "Design" }, { "name" => "Frontend" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "65%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$45K" }] },
            "Owner" => { "type" => "people", "people" => [{ "name" => "Sarah Chen" }] },
            "Notes" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "UI mockups completed, development in progress" }] }
          },
          url: "https://notion.so/website-redesign",
          last_edited: "Jan 10, 2025 14:30"
        },
        {
          title: "Q1 Marketing Campaign",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "Planning" } },
            "Priority" => { "type" => "select", "select" => { "name" => "Medium" } },
            "Due Date" => { "type" => "date", "date" => { "start" => "2025-02-01" } },
            "Team" => { "type" => "multi_select", "multi_select" => [{ "name" => "Marketing" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "25%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$25K" }] },
            "Owner" => { "type" => "people", "people" => [{ "name" => "Mike Johnson" }] },
            "Campaign Type" => { "type" => "select", "select" => { "name" => "Digital" } },
            "Target Audience" => { "type" => "multi_select", "multi_select" => [{ "name" => "B2B" }, { "name" => "Enterprise" }] }
          },
          url: "https://notion.so/q1-marketing",
          last_edited: "Jan 9, 2025 09:15"
        },
        {
          title: "Mobile App v2.0",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "Development" } },
            "Priority" => { "type" => "select", "select" => { "name" => "High" } },
            "Deadline" => { "type" => "date", "date" => { "start" => "2025-03-01" } },
            "Skills Required" => { "type" => "multi_select", "multi_select" => [{ "name" => "Mobile" }, { "name" => "Backend" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "40%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$120K" }] },
            "Lead Developer" => { "type" => "people", "people" => [{ "name" => "Alex Kim" }] },
            "Platform" => { "type" => "multi_select", "multi_select" => [{ "name" => "iOS" }, { "name" => "Android" }] }
          },
          url: "https://notion.so/mobile-app-v2",
          last_edited: "Jan 10, 2025 16:45"
        },
        {
          title: "Security Audit & Compliance",
          properties: {
            "Status" => { "type" => "select", "select" => { "name" => "In Progress" } },
            "Priority" => { "type" => "select", "select" => { "name" => "Critical" } },
            "Deadline" => { "type" => "date", "date" => { "start" => "2025-01-25" } },
            "Teams" => { "type" => "multi_select", "multi_select" => [{ "name" => "Security" }, { "name" => "Legal" }] },
            "Progress" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "80%" }] },
            "Budget" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => "$30K" }] },
            "Lead" => { "type" => "people", "people" => [{ "name" => "Lisa Zhang" }] },
            "Compliance Type" => { "type" => "select", "select" => { "name" => "SOC2" } }
          },
          url: "https://notion.so/security-audit",
          last_edited: "Jan 11, 2025 10:15"
        }
      ]
    end

    def self.build_task(title, status, priority, assignee, estimate, labels)
      {
        title: title,
        properties: {
          "Status" => { "type" => "select", "select" => { "name" => status } },
          "Assignee" => { "type" => "people", "people" => [{ "name" => assignee }] },
          "Priority" => { "type" => "select", "select" => { "name" => priority } },
          "Created" => { "type" => "date", "date" => { "start" => "2025-01-11" } },
          "Estimate" => { "type" => "rich_text", "rich_text" => [{ "plain_text" => estimate }] },
          "Labels" => { "type" => "multi_select", "multi_select" => labels.map { |label| { "name" => label } } }
        },
        url: "https://notion.so/#{title.downcase.gsub(/\s+/, '-')}",
        last_edited: "Recently"
      }
    end
  end
end