- keyname: integration_token
  field_type: text
  name: Notion Integration Token
  description: Your Notion internal integration token (starts with "secret_")
  encrypted: true
  required: true
  placeholder: secret_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

- keyname: display_type
  name: Content Type
  description: What to display from Notion
  field_type: select
  options:
    - Database: database
    - Page: page
  default: database
  required: true

- keyname: database_id
  field_type: text
  name: Database ID
  description: Notion database ID (copy from database URL)
  placeholder: 12345678-1234-1234-1234-123456789abc
  conditional:
    display_type: database

- keyname: page_id
  field_type: text
  name: Page ID
  description: Notion page ID (copy from page URL)
  placeholder: 12345678-1234-1234-1234-123456789abc
  conditional:
    display_type: page

- keyname: instance_name
  name: Display Title
  description: Custom title for your TRMNL display (optional)
  field_type: text
  optional: true
  placeholder: My Project Dashboard

- keyname: max_items
  name: Item Limit
  description: Maximum items to show (databases only)
  field_type: number
  default: 8
  min: 1
  max: 9999
  conditional:
    display_type: database

- keyname: image_height
  name: Image Height
  description: Maximum height for images in pixels (optional)
  field_type: number
  default: 120
  optional: true
  help_text: Set maximum image height in pixels

- keyname: status_field
  name: Status Field
  description: Which property to use as the primary status indicator (optional)
  field_type: text
  optional: true
  placeholder: Status
  help_text: Name of the property to display as a prominent badge (e.g., "Status", "Priority", "State")
  conditional:
    display_type: database

- keyname: sort_property
  name: Sort By
  description: Primary property to sort by (optional)
  field_type: text
  optional: true
  placeholder: Due Date
  help_text: Property name to sort by, or use "created_time" or "last_edited_time" for timestamps
  conditional:
    display_type: database

- keyname: sort_direction
  name: Sort Direction
  description: Sort direction for primary property
  field_type: select
  options:
    - Ascending: ascending
    - Descending: descending
  default: descending
  conditional:
    display_type: database

- keyname: secondary_sort_property
  name: Secondary Sort By
  description: Secondary property for nested sorting (optional)
  field_type: text
  optional: true
  placeholder: Priority
  help_text: Additional property to sort by after primary sort
  conditional:
    display_type: database

- keyname: secondary_sort_direction
  name: Secondary Sort Direction
  description: Sort direction for secondary property
  field_type: select
  options:
    - Ascending: ascending
    - Descending: descending
  default: ascending
  conditional:
    display_type: database

- keyname: multi_column_display
  name: Multi-Column Display
  description: Display items in multiple columns (for half_horizontal and full layouts)
  field_type: select
  options:
    - Single Column: "1"
    - Two Columns: "2"
  default: "2"
  optional: true
  help_text: Enable multi-column layout for better space utilization on wider displays
  conditional:
    display_type: database