module Notion
  module Helpers
    # Property handling helpers
    def primary_badge_for(item)
      # First check for user-configured status field
      status_prop = item[:properties]&.find { |prop| prop[:semantic_type] == :status }
      return status_prop if status_prop

      # Fall back to any badge-type property
      item[:properties]&.find { |prop| prop[:semantic_type] == :badge }
    end

    def visible_properties_for(item, limit = nil)
      props = item[:properties]&.reject do |prop|
        %i[status badge].include?(prop[:semantic_type])
      end || []

      limit ? props.first(limit) : props
    end

    def property_label_classes(prop, size = "small")
      classes = ["label", "label--#{size}"]
      classes << "bg--black text--white px--1" if prop[:semantic_type] == :status
      classes.join(" ")
    end

    # Block handling helpers
    def skip_block?(block, page_title)
      block[:type] == "heading_1" && block[:text] == page_title
    end

    # Block meta symbols - lookup table approach
    BLOCK_SYMBOLS = {
      "bulleted_list_item" => "•",
      "to_do" => ->(block) { block[:checked] ? "✓" : "☐" },
      "quote" => '"',
      "callout" => ->(block) { block[:icon] || "💡" },
      "toggle" => "▸",
      "image" => ->(block) { block[:image_url] ? nil : "🖼️" },
      "child_database" => "📊"
    }.freeze

    def block_meta_content(block, index = nil)
      symbol = BLOCK_SYMBOLS[block[:type]]
      case symbol
      when Proc
        symbol.call(block)
      when String
        symbol
      when nil
        "#{index + 1}." if block[:type] == "numbered_list_item" && index
      end
    end

    def block_meta_classes(block)
      classes = ["index"]
      classes << "label--gray-out" if block[:type] == "to_do" && block[:checked]
      classes.join(" ")
    end

    # Block content classes - lookup table approach
    BLOCK_CLASS_MAP = {
      "heading_1" => { quadrant: "title title--small", default: "title title--large" },
      "heading_2" => { quadrant: "text text--small", default: "title" },
      "heading_3" => { quadrant: "text text--small", default: "title title--small" },
      "to_do" => ->(block) { block[:checked] ? "description label--gray-out" : "description" },
      "quote" => "description text--gray-3",
      "code" => "label label--small text--black"
    }.freeze

    DESCRIPTION_BLOCKS = %w[paragraph bulleted_list_item numbered_list_item callout toggle].freeze

    def block_content_classes(block, layout_type = nil)
      block_type = block[:type]

      # Check for description blocks first
      return "description" if DESCRIPTION_BLOCKS.include?(block_type)

      mapping = BLOCK_CLASS_MAP[block_type]
      case mapping
      when Hash
        mapping[layout_type&.to_sym] || mapping[:default]
      when Proc
        mapping.call(block)
      when String
        mapping
      else
        "description" # default fallback
      end
    end

    def show_image_placeholder?(block)
      block[:type] == "image" && !block[:image_url]
    end

    def show_database_placeholder?(block)
      block[:type] == "child_database"
    end

    # Error handling helpers - consolidated
    ERROR_MESSAGES = {
      title: {
        full: ->(error) { error&.include?("connection") ? "Connection Error" : "No Content Available" },
        short: ->(error) { error&.include?("connection") ? "Connection Error" : "No Content" },
        mini: ->(error) { error&.include?("connection") ? "Error" : "No Content" }
      },
      description: {
        full: ->(error) { error || "Check your Notion configuration and try again" },
        short: ->(error) { error || "Check your Notion configuration" },
        mini: ->(error) { error || "Check config" }
      }
    }.freeze

    def error_message(type, length, error = nil)
      ERROR_MESSAGES.dig(type, length)&.call(error)
    end

    # Backward compatibility methods
    def error_title(error) = error_message(:title, :full, error)
    def error_title_short(error) = error_message(:title, :short, error)
    def error_title_mini(error) = error_message(:title, :mini, error)
    def error_description(error) = error_message(:description, :full, error)
    def error_description_short(error) = error_message(:description, :short, error)
    def error_description_mini(error) = error_message(:description, :mini, error)

    def current_layout
      @current_layout || "full"
    end
  end
end