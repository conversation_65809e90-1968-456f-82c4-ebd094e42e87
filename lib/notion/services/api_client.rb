require "httparty"

module Notion
  module ApiClient
    include HTTParty

    NOTION_API_BASE = "https://api.notion.com/v1".freeze
    NOTION_VERSION = "2022-06-28".freeze

    def get_database(database_id)
      get("/databases/#{database_id}")
    end

    def query_database(database_id, options = {})
      post("/databases/#{database_id}/query", options)
    end

    def get_page(page_id)
      get("/pages/#{page_id}")
    end

    def get_page_blocks(page_id)
      get("/blocks/#{page_id}/children")
    end

    def get_block_children(block_id)
      get("/blocks/#{block_id}/children")
    end

    private

    def get(path, query = {})
      make_request(:get, path, query: query)
    end

    def post(path, body = {})
      make_request(:post, path, body: body.to_json)
    end

    def make_request(method, path, options = {})
      retry_count = 0

      begin
        HTTParty.send(method, "#{NOTION_API_BASE}#{path}", {
          headers: headers,
          timeout: 10
        }.merge(options))
      rescue Net::ReadTimeout, Net::OpenTimeout => e
        retry_count += 1
        sleep retry_count
        retry if retry_count < 3

        { success?: false, code: 408, message: "Request timeout" }
      rescue StandardError => e
        { success?: false, code: 500, message: e.message }
      end
    end

    def headers
      {
        "Authorization" => "Bearer #{@access_token}",
        "Notion-Version" => NOTION_VERSION,
        "Content-Type" => "application/json"
      }
    end
  end

  # Merged SortBuilder class
  class SortBuilder
    TIMESTAMP_FIELDS = %w[created_time last_edited_time].freeze

    def self.build(primary_prop, primary_dir, secondary_prop, secondary_dir)
      sorts = []
      sorts << build_sort_object(primary_prop, primary_dir) if present?(primary_prop)
      sorts << build_sort_object(secondary_prop, secondary_dir) if present?(secondary_prop)
      sorts.empty? ? default_sort : sorts
    end

    def self.build_sort_object(property, direction)
      return nil if property.nil? || property.empty?

      if TIMESTAMP_FIELDS.include?(property)
        { "timestamp" => property, "direction" => direction }
      else
        { "property" => property, "direction" => direction }
      end
    end

    def self.default_sort
      [{ "timestamp" => "last_edited_time", "direction" => "descending" }]
    end

    def self.present?(value)
      value && !value.to_s.strip.empty?
    end
  end
end