# Merged PropertyManager and BlockManager for content processing
module Notion
  module PropertyManager
    def extract_properties(properties)
      return [] unless properties

      properties.filter_map do |key, value|
        next if %w[name title].include?(key.downcase)

        {
          name: key,
          type: value["type"],
          value: format_property_value(value),
          raw_value: value,
          semantic_type: determine_semantic_type(value["type"], key)
        }
      end
    end

    def format_property_value(property)
      case property["type"]
      when "rich_text", "title"
        property[property["type"]].map { |text| text["plain_text"] }.join
      when "number"
        property["number"]
      when "select"
        property["select"]&.dig("name")
      when "multi_select"
        property["multi_select"]&.map { |option| option["name"] }&.join(", ")
      when "date"
        format_date(property["date"]&.dig("start"))
      when "checkbox"
        property["checkbox"] ? "Yes" : "No"
      when "url"
        property["url"]
      when "email"
        property["email"]
      when "phone_number"
        property["phone_number"]
      when "people"
        property["people"]&.map { |person| person["name"] }&.join(", ")
      else
        property["plain_text"] || property.to_s
      end
    end

    def determine_semantic_type(notion_type, property_name)
      return :status if property_name == status_field

      case notion_type
      when "status", "select" then :badge
      when "date" then :temporal
      when "multi_select" then :chips
      when "number" then :numeric
      when "people" then :person
      when "checkbox" then :text
      else :text # rubocop:disable Lint/DuplicateBranch
      end
    end

    def format_date(date_string)
      return "" unless date_string

      Date.parse(date_string).strftime("%b %d, %Y")
    rescue StandardError
      date_string
    end

    private

    def status_field
      respond_to?(:settings) ? settings["status_field"]&.strip : nil
    end
  end

  module BlockManager
    def format_blocks(blocks)
      formatted_blocks = []

      blocks.each do |block|
        formatted_block = {
          type: block["type"],
          text: extract_text_from_block(block),
          checked: block[block["type"]]&.dig("checked"),
          language: block[block["type"]]&.dig("language"),
          icon: block[block["type"]]&.dig("icon", "emoji"),
          image_url: extract_image_url_from_block(block),
          caption: extract_image_caption_from_block(block)
        }

        if %w[column_list column].include?(block["type"]) && block["has_children"]
          child_blocks = fetch_child_blocks(block["id"])
          formatted_blocks.concat(format_blocks(child_blocks))
        else
          formatted_blocks << formatted_block

          if block["has_children"] && !%w[column_list column].include?(block["type"])
            child_blocks = fetch_child_blocks(block["id"])
            formatted_blocks.concat(format_blocks(child_blocks))
          end
        end
      end

      formatted_blocks
    end

    def fetch_child_blocks(block_id)
      response = get_block_children(block_id)
      if response.success?
        response.parsed_response["results"] || []
      else
        []
      end
    rescue StandardError => e
      puts "⚠️  [Notion Block] Error fetching child blocks: #{e.message}"
      []
    end

    def extract_text_from_block(block)
      block_type = block["type"]
      rich_text = block[block_type]&.dig("rich_text") || block[block_type]&.dig("text")

      return "" unless rich_text

      rich_text.map { |text| text["plain_text"] }.join
    end

    def extract_image_url_from_block(block)
      return nil unless block["type"] == "image"

      image_data = block["image"]
      return nil unless image_data

      case image_data["type"]
      when "external"
        image_data["external"]&.dig("url")
      when "file"
        image_data["file"]&.dig("url")
      end
    end

    def extract_image_caption_from_block(block)
      return nil unless block["type"] == "image"

      image_data = block["image"]
      return nil unless image_data && image_data["caption"]

      image_data["caption"].map { |text| text["plain_text"] }.join
    rescue StandardError
      nil
    end
  end
end