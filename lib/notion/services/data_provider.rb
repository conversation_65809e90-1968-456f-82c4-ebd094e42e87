require "httparty"
require "ostruct"
require_relative "../data/mock_data"
require_relative "api_client"
require_relative "content_processor"
require_relative "response_parser"
require_relative "../core/configuration"

module Notion
  class DataProvider
    include ApiClient
    include PropertyManager
    include BlockManager
    include ResponseParser
    include Utilities

    attr_reader :settings

    def initialize(settings, access_token = nil)
      @settings = settings
      @access_token = access_token
      @use_mock = access_token.nil? || access_token.empty?
    end

    def database_items
      return mock_database_items if @use_mock
      return [] unless present?(database_id)

      fetch_with_error_handling("database items") do
        response = query_database(database_id, {
                                    page_size: max_items,
                                    sorts: ::Notion::SortBuilder.build(sort_property, sort_direction, secondary_sort_property, secondary_sort_direction)
                                  })

        return [] unless response.success?

        (response.parsed_response["results"] || []).map { |item| transform_database_item(item) }
      end
    end

    def page_content
      return ::Notion::MockData.page_content_for(page_id) if @use_mock
      return {} unless present?(page_id)

      fetch_with_error_handling("page content", {}) do
        page_response = get_page(page_id)
        return {} unless page_response.success?

        blocks_response = get_page_blocks(page_id)
        return {} unless blocks_response.success?

        transform_page_content(page_response.parsed_response, blocks_response.parsed_response["results"] || [])
      end
    end

    def database_title
      resolve_title(:database, database_id, "Database")
    end

    def page_title
      resolve_title(:page, page_id, "Page")
    end

    private

    # Title resolution
    def resolve_title(type, id, fallback)
      return instance_name if present?(instance_name)
      return ::Notion::MockData.send("#{type}_title_for", id) if @use_mock
      return fallback unless present?(id)

      fetch_title_from_api(type, id) || fallback
    end

    def fetch_title_from_api(type, id)
      response = type == :database ? get_database(id) : get_page(id)
      return nil unless response.success?

      data = response.parsed_response
      title_property = type == :database ? data["title"] : data["properties"]
      extract_title_from_properties(title_property)
    rescue StandardError
      nil
    end

    # Data transformation
    def transform_database_item(item)
      {
        title: extract_title_from_properties(item["properties"]),
        properties: extract_properties(item["properties"]),
        url: item["url"],
        last_edited: format_date(item["last_edited_time"])
      }
    end

    def transform_page_content(page_data, blocks)
      {
        blocks: format_blocks(blocks),
        properties: extract_properties(page_data["properties"]),
        url: page_data["url"],
        last_edited: format_date(page_data["last_edited_time"])
      }
    end

    # Error handling wrapper
    def fetch_with_error_handling(operation, default_return = [])
      yield
    rescue StandardError => e
      log_error("Failed to fetch #{operation}: #{e.message}")
      default_return
    end

    # Mock data methods
    def mock_database_items
      raw_data = ::Notion::MockData.database_items_for(database_id)
      raw_data.map do |item|
        {
          title: item[:title],
          properties: extract_properties(item[:properties]),
          url: item[:url],
          last_edited: item[:last_edited]
        }
      end
    end

    setting_accessors = {
      status_field: { key: "status_field", transform: :strip },
      sort_property: { key: "sort_property", transform: :strip },
      sort_direction: { key: "sort_direction", default: "descending" },
      secondary_sort_property: { key: "secondary_sort_property", transform: :strip },
      secondary_sort_direction: { key: "secondary_sort_direction", default: "ascending" },
      max_items: { key: "max_items", default: 8, transform: :to_i },
      display_type: { key: "display_type", default: "database" },
      database_id: { key: "database_id" },
      page_id: { key: "page_id" },
      instance_name: { key: "instance_name", default: "Notion" }
    }.freeze

    setting_accessors.each do |method_name, config|
      define_method(method_name) do
        value = settings[config[:key]]
        value = value&.send(config[:transform]) if config[:transform] && value
        value || config[:default]
      end
    end
  end
end