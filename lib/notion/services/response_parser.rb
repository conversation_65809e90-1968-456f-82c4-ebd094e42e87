module Notion
  module Response<PERSON><PERSON><PERSON>
    def extract_title_from_properties(title_property)
      return "Untitled" unless title_property

      if title_property.is_a?(Hash)
        %w[Name Title name title].each do |key|
          if title_property[key] && title_property[key]["title"]
            title_array = title_property[key]["title"]
            return title_array.map { |part| part["plain_text"] || part["text"]["content"] }.join if title_array.any?
          end
        end

        title_property.each_value do |value|
          if value["type"] == "title" && value["title"]
            title_array = value["title"]
            return title_array.map { |part| part["plain_text"] || part["text"]["content"] }.join if title_array.any?
          end
        end
      elsif title_property.is_a?(Array)
        if title_property.any?
          return title_property.map do |part|
            part["plain_text"] || part["text"]["content"]
          end.join
        end
      end

      "Untitled"
    rescue StandardError => e
      log_error("Error extracting title: #{e.message}")
      "Untitled"
    end

    def extract_title_from_response(data, type)
      title_property = case type
                       when "database"
                         data["title"]
                       when "page"
                         data["properties"]
                       else
                         return nil
                       end

      return nil unless title_property

      if title_property.is_a?(Hash)
        %w[Name Title name title].each do |key|
          next unless title_property[key] && title_property[key]["title"]

          title_array = title_property[key]["title"]
          if title_array.any?
            return title_array.map do |part|
              part["plain_text"] || part.dig("text", "content")
            end.join
          end
        end

        title_property.each_value do |value|
          next unless value.is_a?(Hash) && value["type"] == "title" && value["title"]

          title_array = value["title"]
          if title_array.any?
            return title_array.map do |part|
              part["plain_text"] || part.dig("text", "content")
            end.join
          end
        end
      elsif title_property.is_a?(Array)
        if title_property.any?
          return title_property.map do |part|
            part["plain_text"] || part.dig("text", "content")
          end.join
        end
      end

      nil
    rescue StandardError
      nil
    end

    private

    def log_error(message)
      puts "⚠️  [Notion API] #{message}"
    end
  end
end