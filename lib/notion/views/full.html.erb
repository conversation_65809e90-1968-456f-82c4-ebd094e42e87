<% instance_variable_set(:@current_layout, "full") %>

<div class="view view--full">
  <% if defined?(error) && error %>
    <%= render 'shared/error_state', error: error, helpers: helpers, current_layout: "full" %>
  <% elsif display_type == "database" %>
    <div class="layout">
      <% if items && items.any? %>
        <div class="list" data-list-limit="true" data-list-max-height="370" data-list-max-columns="<%= multi_column_display %>" data-list-hidden-count="true">
          <% items.each_with_index do |item, idx| %>
            <%= render 'shared/database_item', item: item, index: idx, helpers: helpers, current_layout: "full" %>
          <% end %>
        </div>
      <% else %>
        <%= render 'shared/error_state', error: nil, helpers: helpers, current_layout: "full" %>
      <% end %>
    </div>
  <% elsif display_type == "page" %>
    <div class="layout">
      <% if content && content[:blocks] && content[:blocks].any? %>
        <div class="list" data-list-limit="true" data-list-max-height="420" data-list-max-columns="<%= multi_column_display %>">
          <% content[:blocks].each_with_index do |block, idx| %>
            <%= render 'shared/block_item', block: block, index: idx, helpers: helpers, current_layout: "full", page_title: title %>
          <% end %>
        </div>
      <% else %>
        <%= render 'shared/error_state', error: nil, helpers: helpers, current_layout: "full" %>
      <% end %>
    </div>
  <% end %>

  <%= render 'shared/title_bar', title: title %>
</div>
