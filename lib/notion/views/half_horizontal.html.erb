<% instance_variable_set(:@current_layout, "half_horizontal") %>

<div class="view view--half_horizontal">
  <% if defined?(error) && error %>
    <%= render 'shared/error_state', error: error, helpers: helpers, current_layout: "half_horizontal" %>
  <% elsif display_type == "database" %>
    <div class="layout layout--row">
      <% if items && items.any? %>
        <div class="list" data-list-limit="true" data-list-max-height="150" data-list-max-columns="<%= multi_column_display %>" data-list-hidden-count="true">
          <% items.each_with_index do |item, idx| %>
            <%= render 'shared/database_item', item: item, index: idx, helpers: helpers, current_layout: "half_horizontal" %>
          <% end %>
        </div>
      <% else %>
        <%= render 'shared/error_state', error: nil, helpers: helpers, current_layout: "half_horizontal" %>
      <% end %>
    </div>
  <% elsif display_type == "page" %>
    <div class="layout layout--row">
      <% if content && content[:blocks] && content[:blocks].any? %>
        <div class="list" data-list-limit="true" data-list-max-height="180" data-list-max-columns="<%= multi_column_display %>">
          <% content[:blocks].each_with_index do |block, idx| %>
            <%= render 'shared/block_item', block: block, index: idx, helpers: helpers, current_layout: "half_horizontal", page_title: title %>
          <% end %>
        </div>
      <% else %>
        <%= render 'shared/error_state', error: nil, helpers: helpers, current_layout: "half_horizontal" %>
      <% end %>
    </div>
  <% end %>

  <%= render 'shared/title_bar', title: title %>
</div>
