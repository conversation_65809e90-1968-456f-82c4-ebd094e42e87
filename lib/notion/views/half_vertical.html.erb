<% instance_variable_set(:@current_layout, "half_vertical") %>

<div class="view view--half_vertical">
  <% if defined?(error) && error %>
    <%= render 'shared/error_state', error: error, helpers: helpers, current_layout: "half_vertical" %>
  <% elsif display_type == "database" %>
    <div class="layout layout--col">
      <% if items && items.any? %>
        <div class="list" data-list-limit="true" data-list-max-height="390" data-list-hidden-count="true">
          <% items.each_with_index do |item, idx| %>
            <%= render 'shared/database_item', item: item, index: idx, helpers: helpers, current_layout: "half_vertical" %>
          <% end %>
        </div>
      <% else %>
        <%= render 'shared/error_state', error: nil, helpers: helpers, current_layout: "half_vertical" %>
      <% end %>
    </div>
  <% elsif display_type == "page" %>
    <div class="layout layout--col">
      <% if content && content[:blocks] && content[:blocks].any? %>
        <div class="list" data-list-limit="true" data-list-max-height="420">
          <% content[:blocks].each_with_index do |block, idx| %>
            <%= render 'shared/block_item', block: block, index: idx, helpers: helpers, current_layout: "half_vertical", page_title: title %>
          <% end %>
        </div>
      <% else %>
        <%= render 'shared/error_state', error: nil, helpers: helpers, current_layout: "half_vertical" %>
      <% end %>
    </div>
  <% end %>

  <%= render 'shared/title_bar', title: title %>
</div>
