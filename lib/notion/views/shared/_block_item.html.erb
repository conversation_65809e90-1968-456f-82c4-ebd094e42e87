<% layout_type = current_layout || "full" %>
<% return if skip_block?(block, page_title) %>

<% if %w[heading_1 heading_2 heading_3].include?(block[:type]) %>
  <div class="item">
    <div class="meta"></div>
    <div class="content">
      <span class="<%= block_content_classes(block, layout_type) %>" data-pixel-perfect="true"><%= block[:text] %></span>
    </div>
  </div>
<% elsif block[:type] == "image" %>
  <% if block[:image_url] %>
    <div class="item">
      <div class="meta"></div>
      <div class="content">
        <img src="<%= block[:image_url] %>" style="max-width: 100%; max-height: <%= helpers.image_height %>px" alt="<%= block[:caption] || 'Image' %>">
        <% if block[:caption] %>
          <span class="label label--small" data-pixel-perfect="true"><%= block[:caption] %></span>
        <% end %>
      </div>
    </div>
  <% elsif show_image_placeholder?(block) %>
    <div class="item">
      <div class="meta">
        <span class="<%= block_meta_classes(block) %>"><%= block_meta_content(block, index) %></span>
      </div>
      <div class="content">
        <span class="<%= helpers.block_content_classes(block, layout_type) %>" data-pixel-perfect="true">Image placeholder</span>
      </div>
    </div>
  <% end %>
<% elsif show_database_placeholder?(block) %>
  <div class=" item ">
    <div class=" meta ">
      <span class="<%= block_meta_classes(block) %>"><%= block_meta_content(block, index) %></span>
    </div>
    <div class=" content ">
      <span class="<%= helpers.block_content_classes(block, layout_type) %>" data-pixel-perfect=" true ">Database view</span>
    </div>
  </div>
<% else %>
  <div class=" item ">
    <div class=" meta ">
      <% if (meta_content = block_meta_content(block, index)) %>
        <span class="<%= helpers.block_meta_classes(block) %>"><%= meta_content %></span>
      <% end %>
    </div>
    <div class=" content ">
      <span class="<%= block_content_classes(block, layout_type) %>" data-pixel-perfect=" true "><%= block[:text] %></span>
    </div>
  </div>
<% end %>
