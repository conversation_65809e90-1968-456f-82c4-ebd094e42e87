<%# 
  Shared partial for rendering lists of content (database items or page blocks)
  
  Locals:
  - display_type: 'database' or 'page'
  - content: items array (for database) or content hash with blocks (for page)
  - helpers: helper module instance
  - current_layout: current layout name
  - multi_column_display: whether to display in multiple columns
  - title: page title (only used for page display type)
%>

<% layout_modifier = helpers.layout_modifier(current_layout) %>
<% has_content = helpers.has_content?(content, display_type) %>

<div class="layout<%= layout_modifier ? " #{layout_modifier}" : "" %>">
  <% if has_content %>
    <% list_attrs = helpers.list_attributes(display_type, current_layout, multi_column_display) %>
    <div <%= helpers.attributes_to_html(list_attrs) %>>
      <% if display_type == "database" %>
        <% content.each_with_index do |item, idx| %>
          <%= render 'shared/database_item', item: item, index: idx, helpers: helpers, current_layout: current_layout %>
        <% end %>
      <% else %>
        <% content[:blocks].each_with_index do |block, idx| %>
          <%= render 'shared/block_item', block: block, index: idx, helpers: helpers, current_layout: current_layout, page_title: title %>
        <% end %>
      <% end %>
    </div>
  <% else %>
    <%= render 'shared/error_state', error: nil, helpers: helpers, current_layout: current_layout %>
  <% end %>
</div>