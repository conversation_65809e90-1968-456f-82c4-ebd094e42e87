<% layout_type = current_layout || "full" %>
<% property_limit = case layout_type
                    when "quadrant" then 0
                    when "half_horizontal" then 2
                    when "half_vertical" then 4
                    else 6
                    end %>

<div class="item">
  <div class="meta">
    <span class="index"><%= index + 1 %></span>
  </div>
  <div class="content">
    <% if layout_type == "quadrant" %>
      <span class="title title--small" data-pixel-perfect="true"><%= item[:title] %></span>
      <% if (badge = primary_badge_for(item)) %>
        <span class="<%= property_label_classes(badge, 'xsmall') %>" data-pixel-perfect="true"><%= badge[:value] %></span>
      <% end %>
    <% else %>
      <span class="title title--small" data-pixel-perfect="true"><%= item[:title] %></span>

      <% if (badge = primary_badge_for(item)) %>
        <span class="<%= property_label_classes(badge, 'small') %>" data-pixel-perfect="true"><%= badge[:value] %></span>
      <% end %>

      <% if property_limit > 0 %>
        <% visible_props = visible_properties_for(item, property_limit) %>
        <% if visible_props.any? %>
          <div class="flex gap--xsmall">
            <% visible_props.each do |prop| %>
              <% case prop[:semantic_type]
                 when :date, :temporal %>
                <span class="<%= property_label_classes(prop, 'small') %> label--underline" data-pixel-perfect="true"><%= prop[:value] %></span>
              <% when :person %>
                <span class="<%= property_label_classes(prop, 'small') %>" data-pixel-perfect="true"><%= prop[:value] %></span>
              <% when :progress %>
                <span class="<%= property_label_classes(prop, 'small') %>" data-pixel-perfect="true"><%= prop[:value] %></span>
              <% when :chips %>
                <span class="<%= property_label_classes(prop, 'small') %>" data-pixel-perfect="true"><%= prop[:value] %></span>
              <% when :numeric %>
                <span class="<%= property_label_classes(prop, 'small') %>" data-pixel-perfect="true"><%= prop[:value] %></span>
              <% else %>
                <span class="<%= property_label_classes(prop, 'small') %>" data-pixel-perfect="true"><%= prop[:value] %></span>
              <% end %>
            <% end %>
          </div>
        <% end %>
      <% end %>
    <% end %>
  </div>
</div>
