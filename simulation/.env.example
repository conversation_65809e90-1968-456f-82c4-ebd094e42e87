#NB: MOCK DATA WILL BE SHOWN IF THESE VALUES ARE NOT SET

# TRMNL Notion Plugin - Development Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# NOTION API CONFIGURATION
# =============================================================================
# Get these values by following the setup guide: ../lib/notion/NOTION_SETUP.md

# Your Notion Integration Token (starts with "secret_")
# Create at: https://www.notion.so/my-integrations
# NOTION_INTEGRATION_TOKEN=secret_your_integration_token_here

# Database ID (for database display type)
# Copy from your database URL: the long string between last "/" and "?"
# NOTION_DATABASE_ID=your_database_id_here

# Page ID (for page display type) 
# Copy from your page URL: the long string after the last "-"
# NOTION_PAGE_ID=your_page_id_here
