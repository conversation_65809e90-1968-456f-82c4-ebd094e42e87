GEM
  remote: https://rubygems.org/
  specs:
    actionview (7.2.2.1)
      activesupport (= 7.2.2.1)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activesupport (7.2.2.1)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    ast (2.4.3)
    awesome_print (1.9.2)
    base64 (0.3.0)
    benchmark (0.4.1)
    better_html (2.1.1)
      actionview (>= 6.0)
      activesupport (>= 6.0)
      ast (~> 2.0)
      erubi (~> 1.4)
      parser (>= 2.4)
      smart_properties
    bigdecimal (3.2.2)
    builder (3.3.0)
    cgi (0.5.0)
    colorize (0.8.1)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    csv (3.3.5)
    drb (2.2.3)
    erb (4.0.4)
      cgi (>= 0.3.3)
    erb_lint (0.9.0)
      activesupport
      better_html (>= 2.0.1)
      parser (>= 2.7.1.4)
      rainbow
      rubocop (>= 1)
      smart_properties
    erubi (1.13.1)
    eventmachine (1.2.7)
    faye-websocket (0.11.4)
      eventmachine (>= 0.12.0)
      websocket-driver (>= 0.5.1, < 0.8.0)
    ffi (1.17.2)
    ffi (1.17.2-arm64-darwin)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    json (2.12.2)
    language_server-protocol (3.17.0.5)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mini_mime (1.1.5)
    minitest (5.25.5)
    multi_json (1.16.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    mustermann (3.0.3)
      ruby2_keywords (~> 0.0.1)
    nio4r (2.7.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    ostruct (0.6.2)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    prism (1.4.0)
    puma (6.6.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rainbow (3.1.1)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    regexp_parser (2.10.0)
    rubocop (1.78.0)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.45.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.45.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    securerandom (0.4.1)
    sinatra (3.2.0)
      mustermann (~> 3.0)
      rack (~> 2.2, >= 2.2.4)
      rack-protection (= 3.2.0)
      tilt (~> 2.0)
    sinatra-contrib (3.2.0)
      multi_json (>= 0.0.2)
      mustermann (~> 3.0)
      rack-protection (= 3.2.0)
      sinatra (= 3.2.0)
      tilt (~> 2.0)
    smart_properties (1.17.0)
    tilt (2.6.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)

PLATFORMS
  arm64-darwin-24

DEPENDENCIES
  activesupport (~> 7.0)
  awesome_print (~> 1.9)
  better_html
  colorize (~> 0.8)
  erb (~> 4.0)
  erb_lint
  faye-websocket (~> 0.11)
  httparty (~> 0.21)
  listen (~> 3.8)
  ostruct
  puma (~> 6.0)
  rake (~> 13.0)
  rubocop (~> 1.50)
  sinatra (~> 3.0)
  sinatra-contrib (~> 3.0)

BUNDLED WITH
   2.6.9
