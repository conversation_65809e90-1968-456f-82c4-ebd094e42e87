#!/usr/bin/env ruby

require "erb"
require "fileutils"
require_relative "lib/plugins/base"
require_relative "../lib/notion/notion"
require_relative "../lib/notion/services/data_provider"
require_relative "../lib/notion/data/mock_data"
require_relative "../lib/notion/helpers/notion_helpers"

class ViewExporter
  LAYOUTS = %w[full half_vertical half_horizontal quadrant].freeze
  EXPORT_DIR = "exports".freeze

  def initialize
    @base_path = File.expand_path("../lib/notion/views", __dir__)
    @export_path = File.expand_path(EXPORT_DIR, __dir__)
    setup_export_directory
  end

  def export_all
    puts "🚀 Starting view export process..."

    # Get all mock variants
    mock_variants = ::Notion::MockData.simulation_variants

    mock_variants.each do |variant_name, settings|
      puts "\n📄 Exporting variant: #{variant_name}"
      export_variant(variant_name, settings)
    end

    puts "\n✅ Export complete! Files saved to: #{@export_path}"
    puts "📂 Open exports/index.html to view all exported templates"

    create_index_file(mock_variants.keys)
  end

  def render_partial(partial_name, locals)
    # Handle different partial path formats
    if partial_name.start_with?("plugins/notion/shared/")
      # Format: 'plugins/notion/shared/database_item'
      partial_file = partial_name.split("/").last
      partial_path = File.join(@base_path, "shared/_#{partial_file}.html.erb")
    elsif partial_name.start_with?("shared/")
      # Format: 'shared/database_item'
      partial_file = partial_name.split("/").last
      partial_path = File.join(@base_path, "shared/_#{partial_file}.html.erb")
    else
      # Format: 'database_item'
      partial_path = File.join(@base_path, "shared/_#{partial_name}.html.erb")
    end

    return "<!-- Partial not found: #{partial_path} -->" unless File.exist?(partial_path)

    template_content = File.read(partial_path)
    binding_context = create_binding_context(locals)

    erb = ERB.new(template_content, trim_mode: "-")
    erb.result(binding_context)
  end

  private

  def setup_export_directory
    FileUtils.rm_rf(@export_path)
    FileUtils.mkdir_p(@export_path)
    puts "📁 Created export directory: #{@export_path}"
  end

  def export_variant(variant_name, settings)
    # Create plugin instance with mock data
    plugin = create_plugin_instance(settings)
    locals = plugin.locals

    # Add additional required locals that the templates expect
    locals[:instance_name] = settings["instance_name"] || "Notion Plugin"
    locals[:max_items_for_layout] = locals[:max_items_for_layout] || settings["max_items"]&.to_i || 10

    LAYOUTS.each do |layout|
      puts "  └─ Rendering #{layout} layout..."

      begin
        html_content = render_layout(layout, locals)
        filename = "#{variant_name}_#{layout}.html"
        filepath = File.join(@export_path, filename)

        File.write(filepath, html_content)
        puts "     ✓ Saved: #{filename}"
      rescue StandardError => e
        puts "     ✗ Error rendering #{layout}: #{e.message}"
      end
    end
  end

  def create_plugin_instance(settings)
    # Create mock plugin settings object with settings in the correct place
    plugin_settings = OpenStruct.new(
      settings: settings,
      encrypted_settings: {},
      instance_name: settings["instance_name"]
    )

    # Create plugin instance - will use mock data since no token provided
    Plugins::Notion.new(plugin_settings, {})
  end

  def render_layout(layout, locals)
    template_path = File.join(@base_path, "#{layout}.html.erb")

    raise "Template not found: #{template_path}" unless File.exist?(template_path)

    template_content = File.read(template_path)

    # Create binding with locals
    binding_context = create_binding_context(locals)

    # Render ERB template
    erb = ERB.new(template_content, trim_mode: "-")
    erb.result(binding_context)
  end

  def create_binding_context(locals)
    # Create a binding with all the local variables available
    context = Object.new

    locals.each do |key, value|
      context.instance_variable_set("@#{key}", value)
      context.define_singleton_method(key) { instance_variable_get("@#{key}") }
    end

    # Add render method for partials
    exporter = self
    context.define_singleton_method(:render) do |partial_name, local_vars = {}|
      exporter.render_partial(partial_name, local_vars.merge(locals))
    end

    # Add helper methods if they exist
    if locals[:helpers]
      helpers = locals[:helpers]
      # Get all public instance methods from the helpers object
      helper_methods = helpers.class.included_modules.flat_map(&:instance_methods) + helpers.methods(false)
      helper_methods.uniq.each do |method_name|
        next if context.respond_to?(method_name)
        next if [:class, :instance_eval, :instance_exec].include?(method_name)
        context.define_singleton_method(method_name) do |*args, &block|
          helpers.send(method_name, *args, &block)
        end
      end
    end

    context.instance_eval { binding }
  end

  def create_index_file(variant_names)
    index_content = generate_index_html(variant_names)
    index_path = File.join(@export_path, "index.html")
    File.write(index_path, index_content)
    puts "📋 Created index file: index.html"
  end

  def generate_index_html(variant_names)
    <<~HTML
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>TRMNL Notion Plugin - Exported Views</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; margin: 2rem; }
          .header { border-bottom: 1px solid #eee; padding-bottom: 1rem; margin-bottom: 2rem; }
          .variant-section { margin-bottom: 3rem; }
          .variant-title { font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: #333; }
          .layout-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; }
          .layout-link {#{' '}
            display: block; padding: 1rem; border: 1px solid #ddd; border-radius: 8px;#{' '}
            text-decoration: none; color: #333; transition: all 0.2s;
          }
          .layout-link:hover { border-color: #0066cc; background: #f8f9fa; }
          .layout-name { font-weight: 500; margin-bottom: 0.5rem; }
          .layout-desc { font-size: 0.875rem; color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>TRMNL Notion Plugin - Exported Views</h1>
          <p>All view templates rendered with mock data variants</p>
        </div>

        #{variant_names.map { |variant| generate_variant_section(variant) }.join("\n")}
      </body>
      </html>
    HTML
  end

  def generate_variant_section(variant_name)
    <<~HTML
      <div class="variant-section">
        <h2 class="variant-title">#{variant_name.gsub('_', ' ').split.map(&:capitalize).join(' ')}</h2>
        <div class="layout-grid">
          #{LAYOUTS.map { |layout| generate_layout_link(variant_name, layout) }.join("\n          ")}
        </div>
      </div>
    HTML
  end

  def generate_layout_link(variant_name, layout)
    filename = "#{variant_name}_#{layout}.html"
    layout_display = layout.gsub("_", " ").split.map(&:capitalize).join(" ")

    <<~HTML
      <a href="#{filename}" class="layout-link">
        <div class="layout-name">#{layout_display}</div>
        <div class="layout-desc">View #{variant_name.gsub('_', ' ')} data in #{layout_display.downcase} layout</div>
      </a>
    HTML
  end
end

# Run the export
if __FILE__ == $PROGRAM_NAME
  exporter = ViewExporter.new
  exporter.export_all
end