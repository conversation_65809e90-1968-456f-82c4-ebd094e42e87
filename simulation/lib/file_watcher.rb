# frozen_string_literal: true

require "listen"
require "colorize"

class File<PERSON>atch<PERSON>
  def self.start
    # Watch for changes in all plugin files and simulation files
    listener = Listen.to(
      "../lib",
      "lib",
      only: /\.(erb|rb|yaml|html|env)$/
    ) do |modified, added, removed|
      changes = []

      if modified.any?
        files = modified.map { |f| File.basename(f) }
        puts "\n♻️  File changed: #{files.join(', ')}".colorize(:yellow)
        changes.concat(files.map { |f| { type: "modified", file: f } })
      end

      if added.any?
        files = added.map { |f| File.basename(f) }
        puts "\n➕ File added: #{files.join(', ')}".colorize(:green)
        changes.concat(files.map { |f| { type: "added", file: f } })
      end

      if removed.any?
        files = removed.map { |f| File.basename(f) }
        puts "\n➖ File removed: #{files.join(', ')}".colorize(:red)
        changes.concat(files.map { |f| { type: "removed", file: f } })
      end

      # Reload Ruby files and notify WebSocket clients
      if changes.any?
        # Reload affected Ruby files
        changes.each do |change|
          next unless change[:file].end_with?(".rb") && change[:type] != "removed"

          begin
            # Find the full path of the changed file
            possible_paths = [
              File.join(Dir.pwd, "lib", change[:file]),
              File.join(Dir.pwd, "../lib", change[:file]),
              File.join(Dir.pwd, "../lib/notion", change[:file])
            ]

            full_path = possible_paths.find { |p| File.exist?(p) }
            full_path ||= Dir.glob("**/#{change[:file]}").first

            if full_path
              # Clear from Ruby's require cache
              $LOADED_FEATURES.delete_if { |f| f.include?(change[:file]) }

              # Use load to force reload
              load full_path
              puts "   ↻ Reloaded: #{change[:file]}".colorize(:cyan)
            end
          rescue StandardError => e
            puts "   ⚠️  Error reloading #{change[:file]}: #{e.message}".colorize(:red)
          end
        end

        # Rediscover plugins if plugin files changed
        if changes.any? { |c| c[:file].end_with?(".rb") && !c[:file].include?("file_watcher") }
          require_relative "plugin_loader"
          TrmnlServer.plugin_loader.instance_variable_set(:@discovered_plugins, {})
          TrmnlServer.plugin_loader.discover_plugins
          puts "   🔄 Rediscovered plugins".colorize(:cyan)
        end

        require_relative "trmnl_server"
        TrmnlServer.notify_reload({ changes: changes, timestamp: Time.now.to_i })
      end
    end

    listener.start

    puts "👁  Watching for file changes...".colorize(:cyan)

    listener
  end
end
