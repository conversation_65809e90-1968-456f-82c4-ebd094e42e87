# frozen_string_literal: true

require "yaml"
require "pathname"
require_relative "plugins/base"

class PluginLoader
  attr_reader :plugins_paths, :discovered_plugins

  def initialize(plugins_paths = nil)
    @plugins_paths = if plugins_paths.nil?
                       # Default: use Rails structure lib/ directory
                       [
                         File.expand_path("../../lib", __dir__) # lib/ (Rails structure)
                       ]
                     else
                       Array(plugins_paths)
                     end
    @discovered_plugins = {}
    discover_plugins
  end

  def discover_plugins
    @plugins_paths.each do |plugins_path|
      next unless Dir.exist?(plugins_path)

      discover_plugins_in_path(plugins_path)
    end
  end

  def discover_plugins_in_path(plugins_path)
    Dir.glob(File.join(plugins_path, "*/")).each do |plugin_dir|
      next unless File.directory?(plugin_dir)

      plugin_name = File.basename(plugin_dir)
      next if plugin_name == "base" # Skip base directory

      plugin_file = File.join(plugin_dir, "#{plugin_name}.rb")
      next unless File.exist?(plugin_file)

      # Skip if plugin already discovered (avoid duplicates)
      next if @discovered_plugins[plugin_name]

      begin
        # Load the plugin file
        require plugin_file

        # Load simulation extension if it exists
        simulation_file = File.join(plugin_dir, "#{plugin_name}_simulation.rb")
        if File.exist?(simulation_file)
          require simulation_file
          puts "📦 Discovered plugin: #{plugin_name} (with simulation)"
        else
          puts "📦 Discovered plugin: #{plugin_name}"
        end

        # Get plugin info
        plugin_info = extract_plugin_info(plugin_dir, plugin_name)
        @discovered_plugins[plugin_name] = plugin_info
      rescue StandardError => e
        puts "❌ Failed to load plugin #{plugin_name}: #{e.message}"
      end
    end
  end

  def extract_plugin_info(plugin_dir, plugin_name)
    info = {
      name: plugin_name,
      class_name: "Plugins::#{plugin_name.camelize}",
      path: plugin_dir,
      views_path: File.join(plugin_dir, "views"),
      has_form_fields: File.exist?(File.join(plugin_dir, "form_fields.yaml")),
      has_helpers: Dir.exist?(File.join(plugin_dir, "helpers")),
      layouts: discover_layouts(plugin_dir)
    }

    # Load form fields if available
    form_fields_path = File.join(plugin_dir, "form_fields.yaml")
    info[:form_fields] = YAML.load_file(form_fields_path) if File.exist?(form_fields_path)

    info
  end

  def discover_layouts(plugin_dir)
    views_dir = File.join(plugin_dir, "views")
    return [] unless Dir.exist?(views_dir)

    layouts = []
    %w[full half_vertical half_horizontal quadrant].each do |layout|
      layout_file = File.join(views_dir, "#{layout}.html.erb")
      layouts << layout if File.exist?(layout_file)
    end
    layouts
  end

  def get_plugin_class(plugin_name)
    plugin_info = @discovered_plugins[plugin_name]
    return nil unless plugin_info

    Object.const_get(plugin_info[:class_name])
  rescue NameError
    nil
  end

  def create_plugin_instance(plugin_name, settings = {})
    plugin_class = get_plugin_class(plugin_name)
    return nil unless plugin_class

    # Create mock plugin settings
    plugin_settings = MockPluginSettings.new(settings)
    plugin_class.new(plugin_settings)
  end

  def get_plugin_view_path(plugin_name, layout)
    plugin_info = @discovered_plugins[plugin_name]
    return nil unless plugin_info && plugin_info[:layouts].include?(layout)

    File.join(plugin_info[:views_path], "#{layout}.html.erb")
  end

  def available_plugins
    @discovered_plugins.keys
  end

  def get_plugin_info(plugin_name)
    @discovered_plugins[plugin_name]
  end
end

# Mock plugin settings class for simulation
class MockPluginSettings
  attr_accessor :settings, :encrypted_settings, :instance_name

  def initialize(settings = {})
    @settings = ActiveSupport::HashWithIndifferentAccess.new(settings)

    @instance_name = settings["instance_name"] || "Simulation"
  end
end

# String extensions for camelize if not available
class String
  def camelize
    split("_").map(&:capitalize).join
  end
end
