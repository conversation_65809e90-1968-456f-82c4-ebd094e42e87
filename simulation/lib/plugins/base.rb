# frozen_string_literal: true

require "active_support"
require "active_support/core_ext/hash/indifferent_access"
require "active_support/core_ext/string/inflections"
require "ostruct"
require "date"
require "time"

module Plugins
  module Helpers
    # Core date/time helpers available to all plugins
    module DateFormatter
      def date_yyyy_mm
        user.datetime_now.to_date.strftime("%Y-%m")
      end

      def date_yyyy_mm_dd
        user.datetime_now.to_date.to_s
      end

      def format_date(date_string)
        return "" unless date_string

        Date.parse(date_string).strftime("%b %d, %Y")
      rescue StandardError
        date_string
      end
    end

    # Utility helpers for simulation environment
    module Utils
      def present?(value)
        !value.nil? && !(value.respond_to?(:empty?) && value.empty?)
      end

      def blank?(value)
        !present?(value)
      end

      def log_error(message)
        puts "⚠️  #{message}"
      end
    end
  end
end

module Plugins
  class Base
    include Helpers::DateFormatter
    include Helpers::Utils

    attr_reader :plugin_settings, :settings, :user

    def initialize(plugin_settings, _params = {})
      @plugin_settings = plugin_settings
      @settings = plugin_settings.settings || {}
      @user = build_mock_user
    end

    # Plugin must implement this method
    def locals
      raise NotImplementedError, "#{self.class} must implement 'locals' method"
    end

    def locale
      user.locale || "en"
    end

    private

    def build_mock_user
      {
        datetime_now: DateTime.now,
        locale: "en",
        time_zone: "UTC",
        id: 1,
        email: "<EMAIL>"
      }
    end
  end
end
