# frozen_string_literal: true

require "sinatra/base"
require "sinatra/reloader"
require "faye/websocket"
require "json"
require "erb"
require "ostruct"
require "active_support/hash_with_indifferent_access"
require "active_support/core_ext/object/blank"
require_relative "plugins/base"
require_relative "plugin_loader"

class TrmnlServer < Sinatra::Base
  configure :development do
    register Sinatra::Reloader
    also_reload "../lib/**/*.rb"
    also_reload "lib/**/*.rb"
    also_reload "lib/*.rb"
    dont_reload "lib/file_watcher.rb"
  end

  set :views, File.expand_path("views", __dir__)
  set :public_folder, File.expand_path("public", __dir__)

  # Initialize plugin loader
  @@plugin_loader = PluginLoader.new # rubocop:disable Style/ClassVars

  # WebSocket clients for live reload
  @@live_reload_clients = [] # rubocop:disable Style/ClassVars

  def self.notify_reload(data = {})
    payload = { type: "reload", data: data }.to_json
    @@live_reload_clients.each do |ws|
      ws&.send(payload)
    end
  end

  def self.plugin_loader
    @@plugin_loader
  end

  def self.reload_plugins!
    # Clear all loaded plugin files from Ruby's cache
    $LOADED_FEATURES.delete_if { |f| f.include?("/lib/notion/") }

    # Reinitialize plugin loader
    @@plugin_loader = PluginLoader.new # rubocop:disable Style/ClassVars
    puts "🔄 All plugins reloaded!".colorize(:green)
  end

  helpers do
    def plugin_loader
      self.class.plugin_loader
    end

    def render_plugin_template(plugin_name, template, locals = {})
      template_path = plugin_loader.get_plugin_view_path(plugin_name, template)

      if template_path && File.exist?(template_path)
        erb_content = File.read(template_path)

        # Create a custom context for the ERB template
        context = Object.new

        # Add all locals as instance variables and methods
        locals.each do |key, value|
          context.instance_variable_set("@#{key}", value)
          context.define_singleton_method(key) { value }
        end

        # If helpers object is provided, inject all its methods directly into context
        if locals[:helpers]
          helpers_obj = locals[:helpers]
          # Get all methods from the helpers module
          # Use methods - Object.new.methods to get all custom methods including those from included modules
          helper_methods = helpers_obj.methods - Object.new.methods

          # Define each helper method directly on the context
          helper_methods.each do |method_name|
            context.define_singleton_method(method_name) do |*args, &block|
              helpers_obj.send(method_name, *args, &block)
            end
          end

          # Also copy any instance variables from helpers that might be needed
          helpers_obj.instance_variables.each do |var|
            context.instance_variable_set(var, helpers_obj.instance_variable_get(var))
          end
        end

        # Define a render method that can handle Rails-style render calls
        render_partial_method = method(:render_partial)
        helpers_obj = locals[:helpers]
        context.define_singleton_method(:render) do |partial_name, *args|
          # Handle Rails-style render calls with locals
          options = if args.length == 1 && args[0].is_a?(Hash)
                      args[0]
                    else
                      {}
                    end
          # Always pass helpers to partials
          options[:helpers] ||= helpers_obj
          render_partial_method.call(plugin_name, partial_name, options)
        end

        erb_template = ERB.new(erb_content)
        erb_template.result(context.instance_eval { binding })
      else
        "<!-- Plugin template not found: #{plugin_name}/#{template} -->"
      end
    end

    def plugin_instance(plugin_name, settings = {})
      plugin_loader.create_plugin_instance(plugin_name, settings)
    end

    def available_variants
      all_variants = {}

      plugin_loader.discovered_plugins.each do |plugin_name, plugin_info|
        # Each plugin defines its own mock variants
        plugin_class = plugin_loader.get_plugin_class(plugin_name)
        next unless plugin_class

        # Get variants from plugin class if it defines them
        plugin_variants = if plugin_class.respond_to?(:mock_variants)
                            plugin_class.mock_variants
                          else
                            # Default variants based on form fields or generic
                            default_variants_for_plugin(plugin_name, plugin_info)
                          end

        # Prefix variant keys with plugin name to avoid conflicts
        plugin_variants.each do |variant_key, variant_settings|
          prefixed_key = "#{plugin_name}_#{variant_key}"
          all_variants[prefixed_key] = variant_settings.merge("plugin_name" => plugin_name)
        end
      end

      all_variants
    end

    def default_variants_for_plugin(plugin_name, _plugin_info)
      # Simple default variant for any plugin
      {
        "default" => {
          "instance_name" => "#{plugin_name.capitalize} Plugin",
          "plugin_name" => plugin_name
        }
      }
    end

    def parse_variant_key(variant_key)
      # Extract plugin name and variant from key like "plugin_variant_name"
      parts = variant_key.split("_", 2)
      plugin_name = parts[0]
      variant_name = parts[1] || "default"
      [plugin_name, variant_name]
    end

    # Helper methods to match TRMNL's render method
    def render_partial(plugin_name, partial, options = {})
      # Clean up partial path
      partial_path = partial.gsub("plugins/#{plugin_name}/", "")

      plugin_info = plugin_loader.get_plugin_info(plugin_name)
      return "<!-- Plugin not found: #{plugin_name} -->" unless plugin_info

      # Handle partial path resolution
      if partial_path.include?("/")
        # For paths like "shared/title_bar", look for _title_bar.html.erb
        dir_path = File.dirname(partial_path)
        template_name = "_#{File.basename(partial_path)}"
        template_path = File.join(plugin_info[:views_path], dir_path, "#{template_name}.html.erb")
      else
        template_name = partial_path.start_with?("_") ? partial_path : "_#{partial_path}"
        template_path = File.join(plugin_info[:views_path], "#{template_name}.html.erb")
      end

      if File.exist?(template_path)
        erb_template = ERB.new(File.read(template_path))

        # Create context with options and add helper access
        context = Object.new
        options.each do |key, value|
          context.instance_variable_set("@#{key}", value)
          context.define_singleton_method(key) { value }
        end

        # Add helpers method if available from calling context
        context.define_singleton_method(:helpers) { options[:helpers] } if options[:helpers]

        # Also inject helper methods directly into the context
        if options[:helpers]
          helpers_obj = options[:helpers]
          # Get all methods from the helpers module
          # Use methods(false) to get both public instance methods and those from included modules
          helper_methods = helpers_obj.methods - Object.new.methods

          # Define each helper method directly on the context
          helper_methods.each do |method_name|
            context.define_singleton_method(method_name) do |*args, &block|
              helpers_obj.send(method_name, *args, &block)
            end
          end

          # Also copy any instance variables from helpers that might be needed
          helpers_obj.instance_variables.each do |var|
            context.instance_variable_set(var, helpers_obj.instance_variable_get(var))
          end
        end

        # Add render method to context for nested partials
        render_partial_method = method(:render_partial)
        helpers_obj = options[:helpers]
        current_plugin_name = plugin_name
        context.define_singleton_method(:render) do |partial_name, *args|
          # Handle Rails-style render calls with locals
          opts = if args.length == 1 && args[0].is_a?(Hash)
                   args[0]
                 else
                   {}
                 end
          # Always pass helpers to nested partials
          opts[:helpers] ||= helpers_obj
          render_partial_method.call(current_plugin_name, partial_name, opts)
        end

        erb_template.result(context.instance_eval { binding })
      else
        "<!-- Partial not found: #{partial} at #{template_path} -->"
      end
    end
  end

  # Home page with links to all variants
  get "/" do
    erb :index, {}, { variants: available_variants }
  end

  # Enhanced preview with all layouts
  get "/preview/:variant" do
    variant = params[:variant]

    settings = available_variants[variant]
    halt 404, "Variant not found" unless settings

    erb :enhanced_preview, {}, {
      variant: variant,
      variants: available_variants
    }
  end

  # Individual view for specific layout
  get "/view/:variant/:layout" do
    variant = params[:variant]
    layout = params[:layout]

    settings = available_variants[variant]
    halt 404, "Variant not found" unless settings

    # Validate layout
    valid_layouts = %w[full half_vertical half_horizontal quadrant]
    halt 404, "Layout not found" unless valid_layouts.include?(layout)

    erb :single_view, {}, {
      variant: variant,
      layout: layout,
      variants: available_variants
    }
  end

  # Raw plugin output (for iframe embedding)
  get "/raw/:variant/:layout" do
    variant = params[:variant]
    layout = params[:layout]

    puts "📱 Rendering plugin view: #{variant}/#{layout}"

    settings = available_variants[variant]
    halt 404, "Variant not found" unless settings

    plugin_name = settings["plugin_name"]
    halt 404, "Plugin name not specified" unless plugin_name

    plugin = plugin_instance(plugin_name, settings)
    halt 404, "Plugin not found: #{plugin_name}" unless plugin

    begin
      locals = plugin.locals.merge(
        instance_name: settings["instance_name"],
        settings: settings
      )

      # Add the render helper to locals
      locals[:render] = method(:render_partial)

      plugin_html = render_plugin_template(plugin_name, layout, locals)

      # Return simple HTML wrapper for iframe content
      "<!DOCTYPE html>
      <html>
      <head>
        <meta charset='UTF-8'>
        <link rel='stylesheet' href='https://usetrmnl.com/css/latest/plugins.css'>
        <script src='https://usetrmnl.com/js/latest/plugins.js'></script>
      </head>
      <body class='trmnl'>
        #{plugin_html}
        <script>
          // Execute TRMNL plugin enhancements after content loads
          document.addEventListener('DOMContentLoaded', function() {
            if (typeof terminalize === 'function') {
              terminalize();
            }
          });
        </script>
      </body>
      </html>"
    rescue StandardError => e
      "<!DOCTYPE html>
      <html>
      <head>
        <meta charset='UTF-8'>
      </head>
      <body>
        <div style='padding: 20px; color: red;'>
          <h3>Plugin Error</h3>
          <p>#{e.message}</p>
          <pre>#{e.backtrace.join("\n")}</pre>
        </div>
      </body>
      </html>"
    end
  end

  # WebSocket endpoint for live reload
  get "/live_reload" do
    if Faye::WebSocket.websocket?(request.env)
      ws = Faye::WebSocket.new(request.env)

      ws.on(:open) do |_event|
        @@live_reload_clients << ws
        puts "🔌 Live reload client connected (#{@@live_reload_clients.length} total)"
      end

      ws.on(:close) do |_event|
        @@live_reload_clients.delete(ws)
        puts "🔌 Live reload client disconnected (#{@@live_reload_clients.length} total)"
      end

      ws.rack_response
    else
      halt 400, "WebSocket required"
    end
  end

  # Data endpoint for debugging
  get "/data/:variant" do
    content_type :json

    variant = params[:variant]
    settings = available_variants[variant]
    halt 404, { error: "Variant not found" }.to_json unless settings

    plugin_name = settings["plugin_name"]
    halt 404, { error: "Plugin name not specified" }.to_json unless plugin_name

    plugin = plugin_instance(plugin_name, settings)
    halt 404, { error: "Plugin not found: #{plugin_name}" }.to_json unless plugin

    begin
      JSON.pretty_generate({
                             plugin_name: plugin_name,
                             settings: settings,
                             locals: plugin.locals,
                             discovered_plugins: plugin_loader.available_plugins
                           })
    rescue StandardError => e
      JSON.pretty_generate({
                             error: e.message,
                             backtrace: e.backtrace
                           })
    end
  end

  run! if app_file == $PROGRAM_NAME
end
