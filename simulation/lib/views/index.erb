<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plugin Development Server</title>
    <link rel="stylesheet" href="https://usetrmnl.com/css/latest/plugins.css">
    <style>
      * { box-sizing: border-box; }

      body {
        margin: 0;
        padding: 40px;
        background: #fff;
        color: #333;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
      }

      h1 {
        font-size: 36px;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .subtitle {
        color: #666;
        font-size: 18px;
        margin-bottom: 40px;
      }

      .info-box {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
      }

      .info-box h3 {
        margin-top: 0;
        color: #667eea;
      }

      .command {
        background: #f1f3f4;
        padding: 8px 12px;
        border-radius: 4px;
        font-family: monospace;
        display: inline-block;
        margin: 4px 0;
      }

      .variants-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 40px;
      }

      .variant-card {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        transition: all 0.3s;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .variant-card:hover {
        border-color: #667eea;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
      }

      .variant-card h3 {
        margin-top: 0;
        color: #333;
      }

      .variant-meta {
        color: #666;
        font-size: 14px;
        margin-bottom: 15px;
      }

      .layout-links {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }

      .layout-link {
        background: #f8f9fa;
        color: #667eea;
        text-decoration: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.2s;
        border: 1px solid #e9ecef;
      }

      .layout-link:hover {
        background: #667eea;
        color: #fff;
        border-color: #667eea;
      }

      .live-link {
        background: #764ba2;
        color: #fff;
        border-color: #764ba2;
      }

      .live-link:hover {
        background: #8e5eb8;
        border-color: #8e5eb8;
      }

      .api-section {
        margin-top: 40px;
        padding-top: 40px;
        border-top: 1px solid #e9ecef;
      }

      .endpoint {
        background: #f8f9fa;
        padding: 12px;
        border-radius: 4px;
        margin: 8px 0;
        font-family: monospace;
        border: 1px solid #e9ecef;
      }

      .method {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: bold;
        margin-right: 10px;
      }

      .get { background: #4ade80; color: #000; }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>TRMNL Plugin Simulator</h1>
      <p class="subtitle">Development Server - All Plugins</p>
      <%
# Group variants by plugin
      plugins_grouped = variants.group_by { |key, settings| settings['plugin_name'] || 'unknown' }
      %>
      <% plugins_grouped.each do |plugin_name, plugin_variants| %>
        <h2><%= plugin_name.capitalize %> Plugin</h2>
        <div class="variants-grid">
          <% plugin_variants.each do |key, settings| %>
            <div class="variant-card">
              <h3><%= settings['instance_name'] || key %></h3>
              <div class="variant-meta">
                <% if settings['display_type'] %>
                  <%= settings['display_type'].capitalize %>
                <% else %>
                  <%= plugin_name.capitalize %> Plugin
                <% end %>
              </div>
              <div class="layout-links">
                <a href="/preview/<%= key %>" class="layout-link" style="background: #667eea; color: #fff; border-color: #667eea; font-weight: 500;">All Views</a>
                <a href="/view/<%= key %>/full" class="layout-link">Full</a>
                <a href="/view/<%= key %>/half_vertical" class="layout-link">Half V</a>
                <a href="/view/<%= key %>/half_horizontal" class="layout-link">Half H</a>
                <a href="/view/<%= key %>/quadrant" class="layout-link">Quadrant</a>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  </body>
</html>
