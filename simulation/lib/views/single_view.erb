<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= layout.gsub('_', ' ').capitalize %> - <%= variants[variant]['instance_name'] || variant %></title>
  <script src="/trmnl-component.js" defer></script>
  <style>
    * { box-sizing: border-box; }

    body {
      margin: 0;
      background: #f8f9fa;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    .header {
      background: #fff;
      border-bottom: 1px solid #e9ecef;
      padding: 15px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .status {
      width: 8px;
      height: 8px;
      background: #4ade80;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }

    .controls {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .control {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;
      text-decoration: none;
      color: #333;
    }

    .control:hover {
      background: #e9ecef;
    }

    .view-switcher {
      display: flex;
      gap: 4px;
      margin-right: 12px;
    }

    .view-btn {
      background: #9ca3af;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;
      text-decoration: none;
      font-weight: 500;
    }

    .view-btn:hover {
      background: #6b7280;
    }

    .view-btn.active {
      background: #374151;
    }

    .layout-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 30px;
    }

    .layout {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
    }

    .layout-name {
      font-size: 16px;
      font-weight: 500;
      color: #666;
      text-transform: capitalize;
    }

    .device {
      transform: scale(1.2);
      transform-origin: center;
    }

    .data-panel {
      position: fixed;
      right: -350px;
      top: 0;
      width: 350px;
      height: 100%;
      background: #fff;
      border-left: 1px solid #e9ecef;
      transition: right 0.3s ease;
      z-index: 1000;
      display: flex;
      flex-direction: column;
    }

    .data-panel.open {
      right: 0;
    }

    .data-header {
      padding: 15px 20px;
      border-bottom: 1px solid #e9ecef;
      background: #f8f9fa;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .data-content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }

    .data-content pre {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      font-size: 11px;
      overflow-x: auto;
      margin: 0;
    }

    .close {
      background: none;
      border: none;
      font-size: 16px;
      cursor: pointer;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="title">
      <span class="status"></span>
      <%= layout.gsub('_', ' ').capitalize %> - <%= variants[variant]['instance_name'] || variant %>
    </div>

    <div class="controls">
      <div class="view-switcher">
        <a href="/view/<%= variant %>/full" class="view-btn<%= ' active' if layout == 'full' %>">Full</a>
        <a href="/view/<%= variant %>/half_vertical" class="view-btn<%= ' active' if layout == 'half_vertical' %>">Half V</a>
        <a href="/view/<%= variant %>/half_horizontal" class="view-btn<%= ' active' if layout == 'half_horizontal' %>">Half H</a>
        <a href="/view/<%= variant %>/quadrant" class="view-btn<%= ' active' if layout == 'quadrant' %>">Quadrant</a>
      </div>
      <select class="control" id="colorSelect">
        <option value="white">White</option>
        <option value="black">Black</option>
        <option value="mint">Mint</option>
        <option value="gray">Gray</option>
        <option value="wood">Wood</option>
      </select>
      <button class="control" onclick="toggleData()">Data</button>
      <a href="/preview/<%= variant %>" class="control">All Views</a>
      <a href="/" class="control">← Home</a>
    </div>
  </div>

  <div class="layout-container">
    <div class="layout">
      <trmnl-frame class="device" color="white" data-pixel-perfect="true" src="/raw/<%= variant %>/<%= layout %>"></trmnl-frame>
    </div>
  </div>

  <div class="data-panel" id="dataPanel">
    <div class="data-header">
      <h4>Plugin Data</h4>
      <button class="close" onclick="toggleData()">×</button>
    </div>
    <div class="data-content">
      <pre id="dataContent">Loading...</pre>
    </div>
  </div>

  <script>
    let ws = null;
    let dataPanelOpen = false;

    const device = document.querySelector('trmnl-frame');
    const colorSelect = document.getElementById('colorSelect');
    const dataPanel = document.getElementById('dataPanel');
    const dataContent = document.getElementById('dataContent');

    function refresh() {
      const src = device.getAttribute('src');
      device.setAttribute('src', src + '?t=' + Date.now());
      if (dataPanelOpen) loadData();
    }

    function updateStatus(connected) {
      const status = document.querySelector('.status');
      status.style.background = connected ? '#4ade80' : '#ef4444';
    }

    function connectWebSocket() {
      const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
      ws = new WebSocket(`${protocol}//${location.host}/live_reload`);

      ws.onopen = () => updateStatus(true);
      ws.onclose = () => {
        updateStatus(false);
        setTimeout(connectWebSocket, 2000);
      };
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'reload') refresh();
      };
    }

    function toggleData() {
      dataPanelOpen = !dataPanelOpen;
      dataPanel.classList.toggle('open', dataPanelOpen);
      if (dataPanelOpen) loadData();
    }

    async function loadData() {
      try {
        dataContent.textContent = 'Loading...';
        const response = await fetch('/data/<%= variant %>');
        if (response.ok) {
          const data = await response.text();
          dataContent.textContent = data;
        } else {
          dataContent.textContent = `Error: ${response.status} ${response.statusText}`;
        }
      } catch (e) {
        dataContent.textContent = 'Error: ' + e.message;
      }
    }

    colorSelect.addEventListener('change', function() {
      device.setColor(this.value);
    });

    connectWebSocket();

    document.addEventListener('keydown', (e) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'r') {
        e.preventDefault();
        refresh();
      }
    });
  </script>
</body>
</html>
