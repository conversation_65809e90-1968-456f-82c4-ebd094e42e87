#!/usr/bin/env ruby
# frozen_string_literal: true

require "bundler/setup"
require "colorize"

# Load environment variables from .env if it exists
if File.exist?(".env")
  File.readlines(".env").each do |line|
    next if line.strip.empty? || line.strip.start_with?("#")

    key, value = line.strip.split("=", 2)
    ENV[key] = value if key && value
  end
end

# Load server
require_relative "lib/trmnl_server"
require_relative "lib/file_watcher"

puts "🚀 TRMNL Plugin Development Server".colorize(:blue)
puts "===================================\n".colorize(:blue)

puts "📦 Loading plugin architecture...".colorize(:yellow)

puts "\n🌐 Starting web server...".colorize(:yellow)
puts "   Local: #{'http://localhost:4567'.colorize(:light_cyan)}"
puts "   Network: #{'http://0.0.0.0:4567'.colorize(:light_cyan)}"

puts "\n📝 Available features:".colorize(:yellow)
puts "   • #{'Dashboard'.colorize(:light_blue)} - All plugin variants"

# Check for plugin-specific credentials
real_data_detected = false
plugin_tokens = []

# Dynamically check for common plugin environment variables
ENV.each do |key, value|
  next unless key.end_with?("_TOKEN") && !value.empty?

  plugin_name = key.gsub("_TOKEN", "").downcase.capitalize
  plugin_tokens << { name: plugin_name, token: value }
  real_data_detected = true
end

if real_data_detected
  puts "\n✅ Using real plugin data!".colorize(:green)
  plugin_tokens.each do |plugin|
    puts "   #{plugin[:name]} Token: " + "#{plugin[:token][0..15]}...".colorize(:light_cyan)
  end
else
  puts "\n📌 Using mock data".colorize(:yellow)
  puts "   To use real data, create a .env file with plugin credentials".colorize(:yellow)
  puts "   Examples: PLUGIN_TOKEN, API_TOKEN, etc.".colorize(:yellow)
end
puts "   • #{'Enhanced Preview'.colorize(:light_blue)} - All 4 layouts with TRMNL frames"
puts "   • #{'Live Reload'.colorize(:light_blue)} - Instant updates via WebSocket"
puts "   • #{'Color Themes'.colorize(:light_blue)} - Test all device colors"
puts "   • #{'Data Inspection'.colorize(:light_blue)} - Debug plugin state"

puts "\n💡 Tips:".colorize(:magenta)
puts "   • Templates auto-reload when changed"
puts "   • Use Cmd+R to manually refresh"
puts "   • Check console for template errors"
puts "   • Press Ctrl+C to stop the server"

puts "\n#{'─' * 50}\n"

# Start file watcher in background
Thread.new { FileWatcher.start }

# Configure and start server
TrmnlServer.set :bind, "0.0.0.0"
TrmnlServer.set :port, 4567
TrmnlServer.set :server, "puma"
TrmnlServer.set :environment, :development

# Start the server
TrmnlServer.run!
